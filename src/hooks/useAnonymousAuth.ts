import { useAuth } from '../contexts/AuthContext';
import { useCertificate } from '../contexts/CertificateContext';

/**
 * Hook for managing anonymous authentication flows
 */
export const useAnonymousAuth = () => {
  const { user, isAnonymous, signInAnonymously } = useAuth();
  const { createNewCertificate } = useCertificate();

  /**
   * Ensures user is authenticated (either permanent or anonymous)
   * If not authenticated, signs in anonymously
   */
  const ensureAuthenticated = async (): Promise<boolean> => {
    if (user) {
      return true; // Already authenticated
    }

    try {
      const { error, data } = await signInAnonymously();
      if (error) {
        console.error('Anonymous sign-in failed:', error);
        return false;
      }
      // Verify we got a user back
      return !!data?.user;
    } catch (error) {
      console.error('Anonymous sign-in error:', error);
      return false;
    }
  };

  /**
   * Creates a certificate with automatic anonymous authentication if needed
   */
  const createCertificateWithAuth = async (certificateType: string): Promise<string | null> => {
    console.log('🔄 Starting certificate creation with auth...', { certificateType, hasUser: !!user });

    try {
      // If user is already authenticated, proceed directly
      if (user) {
        console.log('✅ User already authenticated, creating certificate directly');
        const certificateId = await createNewCertificate(certificateType);
        console.log('✅ Certificate created successfully:', certificateId);
        return certificateId;
      }

      // If not authenticated, sign in anonymously first
      console.log('🔐 No user found, signing in anonymously...');
      const { error, data } = await signInAnonymously();
      if (error) {
        console.error('❌ Anonymous sign-in failed:', error);
        throw new Error('Authentifizierung fehlgeschlagen');
      }

      // Use the user from the sign-in response directly
      if (!data?.user) {
        console.error('❌ No user data received after authentication');
        throw new Error('Keine Benutzerdaten nach Authentifizierung erhalten');
      }

      console.log('✅ Anonymous sign-in successful, creating certificate...');
      // Create the certificate immediately with the authenticated user
      const certificateId = await createNewCertificate(certificateType);
      console.log('✅ Certificate created successfully:', certificateId);
      return certificateId;
    } catch (error) {
      console.error('❌ Error creating certificate with auth:', error);
      return null;
    }
  };

  /**
   * Gets the user's email from certificate data (for anonymous users)
   */
  const getUserEmailFromCertificate = async (certificateId: string): Promise<string | null> => {
    try {
      const { supabase } = await import('../lib/supabase');
      const { data, error } = await supabase
        .from('energieausweise')
        .select('objektdaten')
        .eq('id', certificateId)
        .single();

      if (error || !data?.objektdaten) {
        return null;
      }

      const objektdaten = data.objektdaten as any;
      return objektdaten?.Kunden_email || null;
    } catch (error) {
      console.error('Error getting email from certificate:', error);
      return null;
    }
  };

  return {
    user,
    isAnonymous,
    ensureAuthenticated,
    createCertificateWithAuth,
    getUserEmailFromCertificate,
  };
};
