import { createClient, SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '../types/supabase';

// Get environment variables
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Validate environment variables
if (!supabaseUrl || !supabaseAnonKey) {
  console.log('Missing Supabase environment variables: ', supabaseUrl, supabaseAnonKey)
  throw new Error('Missing Supabase environment variables');
}

// Validate URL format
try {
  // Test if the URL is valid by constructing a URL object
  new URL(supabaseUrl);
} catch (error) {
  console.error('Invalid Supabase URL:', supabaseUrl);
  throw new Error('Invalid Supabase URL format. Please check your VITE_SUPABASE_URL environment variable.');
}

// Helper function to get the site URL for redirects
const getSiteUrl = () => {
  let url = window.location.origin || 'http://localhost:5173';
  // Make sure URL has trailing slash
  return url.endsWith('/') ? url : `${url}/`;
};

// Helper function to determine if we should detect session in URL
const shouldDetectSessionInUrl = () => {
  // Only detect session in URL for password reset flows
  const currentPath = window.location.pathname;
  const hasAuthParams = window.location.hash.includes('access_token') ||
                       window.location.hash.includes('refresh_token') ||
                       window.location.search.includes('access_token') ||
                       window.location.search.includes('refresh_token');

  // Enable URL detection only for reset password page or when auth params are present
  return currentPath === '/reset-password' || hasAuthParams;
};

// Create the Supabase client
let supabaseInstance: SupabaseClient<Database>;

try {
  supabaseInstance = createClient<Database>(supabaseUrl, supabaseAnonKey, {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: shouldDetectSessionInUrl(),
      flowType: 'pkce',
    },
  });
} catch (error) {
  console.error('Failed to initialize Supabase client:', error);
  // Provide a fallback for development if needed
  if (import.meta.env.DEV) {
    console.warn('Using mock Supabase client for development');
    // This is a minimal mock implementation for development
    supabaseInstance = {
      auth: {
        onAuthStateChange: () => ({ data: null, error: null }),
        getSession: () => Promise.resolve({ data: { session: null }, error: null }),
        signInWithPassword: () => Promise.resolve({ 
          data: { user: null, session: null }, 
          error: null 
        }),
        signUp: () => Promise.resolve({ 
          data: { user: null, session: null }, 
          error: null 
        }),
        signOut: () => Promise.resolve({ error: null }),
        resetPasswordForEmail: () => Promise.resolve({ 
          data: {}, 
          error: null 
        }),
        updateUser: () => Promise.resolve({ 
          data: { user: null }, 
          error: null 
        }),
        getUser: () => Promise.resolve({
          data: { user: { id: 'mock-user-id' } },
          error: null
        }),
        // Add other auth methods as needed
      },
      // Database methods
      from: (table: string) => ({
        upsert: (data: any, _options?: any) => Promise.resolve({
          data: Array.isArray(data) ? data.map(item => ({ ...item, id: `mock-${table}-id` })) : { ...data, id: `mock-${table}-id` },
          error: null
        }),
        select: (_columns?: string) => ({
          eq: (column: string, value: any) => ({
            single: () => Promise.resolve({
              data: { id: `mock-${table}-id`, [column]: value },
              error: null
            })
          })
        })
      }),
      // Add other Supabase methods as needed
    } as unknown as SupabaseClient<Database>;
  } else {
    throw error; // Re-throw in production
  }
}

export const supabase = supabaseInstance;

// Export the getSiteUrl function to use in auth operations that need a redirect URL
export const getAuthRedirectUrl = getSiteUrl;
