<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Input Focus Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-field {
            margin-bottom: 16px;
        }
        label {
            display: block;
            margin-bottom: 4px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 16px;
        }
        input:focus {
            outline: none;
            border-color: #22c55e;
            box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2);
        }
        .test-section {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>Input Focus Test</h1>
    <p>This is a simple test to verify that input fields can receive and maintain focus properly.</p>
    
    <div class="test-section">
        <h2>Test Form</h2>
        <form>
            <div class="form-field">
                <label for="strasse">Straße</label>
                <input type="text" id="strasse" name="strasse" placeholder="Straße des Objektes">
            </div>
            
            <div class="form-field">
                <label for="hausnr">Hausnummer</label>
                <input type="text" id="hausnr" name="hausnr" placeholder="222">
            </div>
            
            <div class="form-field">
                <label for="plz">PLZ</label>
                <input type="text" id="plz" name="plz" placeholder="99423">
            </div>
            
            <div class="form-field">
                <label for="ort">Ort</label>
                <input type="text" id="ort" name="ort" placeholder="Weimar">
            </div>
        </form>
    </div>
    
    <div class="test-section">
        <h2>Test Instructions</h2>
        <ol>
            <li>Click on the "Straße" input field</li>
            <li>Verify that the field receives focus (border should turn green)</li>
            <li>Type some text to verify input works</li>
            <li>Tab to the next field or click on another field</li>
            <li>Repeat for all fields</li>
        </ol>
        <p><strong>Expected behavior:</strong> All input fields should receive focus when clicked and allow text input without losing focus.</p>
    </div>
</body>
</html>
