/**
 * Admin Pricing Management Component
 * 
 * Allows administrators to view and update pricing for different certificate types.
 * Integrates with <PERSON><PERSON> for product/price management.
 */

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { PricingService, type CertificatePricing } from '../../services/pricingService';
import { LoadingSpinner, ErrorMessage } from '../ui/StatusMessages';

interface PricingFormData {
  price_cents: number;
  stripe_product_id?: string;
  stripe_price_id?: string;
}

export const PricingManagement = () => {
  const queryClient = useQueryClient();
  const [editingType, setEditingType] = useState<string | null>(null);
  const [formData, setFormData] = useState<PricingFormData>({
    price_cents: 0
  });

  // Fetch all pricing records
  const { data: pricingRecords, isLoading, error } = useQuery({
    queryKey: ['admin', 'pricing', 'all'],
    queryFn: () => PricingService.getAllPricingRecords(),
    retry: false,
  });

  // Update pricing mutation
  const updatePricingMutation = useMutation({
    mutationFn: async ({ certificateType, data }: { 
      certificateType: string; 
      data: PricingFormData 
    }) => {
      return await PricingService.updatePricing(
        certificateType as any,
        data.price_cents,
        data.stripe_product_id,
        data.stripe_price_id
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'pricing'] });
      queryClient.invalidateQueries({ queryKey: ['pricing'] });
      setEditingType(null);
      setFormData({ price_cents: 0 });
    },
    onError: (error) => {
      console.error('Error updating pricing:', error);
    }
  });

  const handleEdit = (pricing: CertificatePricing) => {
    setEditingType(pricing.certificate_type);
    setFormData({
      price_cents: pricing.price_cents,
      stripe_product_id: pricing.stripe_product_id || '',
      stripe_price_id: pricing.stripe_price_id || ''
    });
  };

  const handleSave = () => {
    if (!editingType) return;
    
    updatePricingMutation.mutate({
      certificateType: editingType,
      data: formData
    });
  };

  const handleCancel = () => {
    setEditingType(null);
    setFormData({ price_cents: 0 });
  };

  const formatEuros = (cents: number) => {
    return `${(cents / 100).toFixed(2).replace('.', ',')} €`;
  };

  if (isLoading) {
    return <LoadingSpinner message="Lade Preisdaten..." />;
  }

  if (error) {
    return <ErrorMessage message="Fehler beim Laden der Preisdaten" />;
  }

  const activePricing = pricingRecords?.filter(p => p.is_active) || [];

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
          Preisverwaltung
        </h3>
        
        <div className="space-y-4">
          {activePricing.map((pricing) => (
            <div key={pricing.id} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-gray-900">
                    {pricing.display_name}
                  </h4>
                  <p className="text-sm text-gray-500 mt-1">
                    {pricing.description}
                  </p>
                  <div className="mt-2 text-sm text-gray-600">
                    <span className="font-medium">Typ:</span> {pricing.certificate_type}
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  {editingType === pricing.certificate_type ? (
                    <div className="flex items-center space-x-2">
                      <div className="flex flex-col space-y-2">
                        <input
                          type="number"
                          value={formData.price_cents}
                          onChange={(e) => setFormData({
                            ...formData,
                            price_cents: parseInt(e.target.value) || 0
                          })}
                          className="w-24 px-2 py-1 text-sm border border-gray-300 rounded"
                          placeholder="Cent"
                        />
                        <input
                          type="text"
                          value={formData.stripe_product_id || ''}
                          onChange={(e) => setFormData({
                            ...formData,
                            stripe_product_id: e.target.value
                          })}
                          className="w-32 px-2 py-1 text-sm border border-gray-300 rounded"
                          placeholder="Stripe Product ID"
                        />
                        <input
                          type="text"
                          value={formData.stripe_price_id || ''}
                          onChange={(e) => setFormData({
                            ...formData,
                            stripe_price_id: e.target.value
                          })}
                          className="w-32 px-2 py-1 text-sm border border-gray-300 rounded"
                          placeholder="Stripe Price ID"
                        />
                      </div>
                      <div className="flex flex-col space-y-1">
                        <button
                          onClick={handleSave}
                          disabled={updatePricingMutation.isPending}
                          className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
                        >
                          {updatePricingMutation.isPending ? 'Speichern...' : 'Speichern'}
                        </button>
                        <button
                          onClick={handleCancel}
                          className="px-3 py-1 text-sm bg-gray-600 text-white rounded hover:bg-gray-700"
                        >
                          Abbrechen
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <div className="text-lg font-semibold text-gray-900">
                          {formatEuros(pricing.price_cents)}
                        </div>
                        <div className="text-xs text-gray-500">
                          {pricing.price_cents} Cent
                        </div>
                        {pricing.stripe_price_id && (
                          <div className="text-xs text-blue-600 mt-1">
                            Stripe: {pricing.stripe_price_id}
                          </div>
                        )}
                      </div>
                      <button
                        onClick={() => handleEdit(pricing)}
                        className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
                      >
                        Bearbeiten
                      </button>
                    </div>
                  )}
                </div>
              </div>
              
              {updatePricingMutation.isError && editingType === pricing.certificate_type && (
                <div className="mt-2 text-sm text-red-600">
                  Fehler beim Aktualisieren: {updatePricingMutation.error?.message}
                </div>
              )}
            </div>
          ))}
        </div>

        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="text-sm font-medium text-blue-900 mb-2">
            Hinweise zur Preisverwaltung
          </h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Preise werden in Cent angegeben (z.B. 4900 für 49,00 €)</li>
            <li>• Stripe Product/Price IDs sind optional, aber empfohlen für bessere Integration</li>
            <li>• Änderungen werden sofort auf der Website sichtbar</li>
            <li>• Nur aktive Preise werden in der Benutzeroberfläche angezeigt</li>
          </ul>
        </div>
      </div>
    </div>
  );
};
