import { useState, useEffect, useCallback } from 'react';
import { useCertificate } from '../contexts/CertificateContext';

// Define page types
export type PageType = 'objektdaten' | 'gebaeudedetails1' | 'gebaeudedetails2' | 'fenster' | 'heizung' | 'tww-lueftung' | 'verbrauch' | 'zusammenfassung';

// Define certificate types
export type CertificateType = 'WG/V' | 'WG/B' | 'NWG/V';

// Navigation state interface
interface NavigationState {
  visitedPages: PageType[];
  highestPageReached: number;
  certificateId: string;
  timestamp: number;
}

// Define the page configuration for each certificate type
const certificateTypePages: Record<CertificateType, PageType[]> = {
  'WG/V': [
    'objektdaten',
    'gebaeudedetails1',
    'gebaeudedetails2',
    'verbrauch',
    'zusammenfassung'
  ],
  'WG/B': [
    'objektdaten',
    'gebaeudedetails1',
    'gebaeudedetails2',
    'fenster',
    'heizung',
    'tww-lueftung',
    'zusammenfassung'
  ],
  'NWG/V': [
    'objektdaten',
    'gebaeudedetails1',
    'gebaeudedetails2',
    'verbrauch',
    'zusammenfassung'
  ]
};

/**
 * Custom hook to manage navigation state for certificate editing flow
 * Tracks visited pages and allows forward navigation to previously visited pages
 */
export const useNavigationState = (certificateType: CertificateType | null) => {
  const { activeCertificateId } = useCertificate();
  const [navigationState, setNavigationState] = useState<NavigationState | null>(null);

  // Get the storage key for the current certificate
  const getStorageKey = useCallback((certId: string) => {
    return `navigationState_${certId}`;
  }, []);

  // Load navigation state from session storage
  const loadNavigationState = useCallback((certId: string): NavigationState | null => {
    try {
      const stored = sessionStorage.getItem(getStorageKey(certId));
      if (stored) {
        const parsed = JSON.parse(stored) as NavigationState;
        // Validate that the stored state is for the correct certificate
        if (parsed.certificateId === certId) {
          return parsed;
        }
      }
    } catch (error) {
      console.error('Error loading navigation state:', error);
    }
    return null;
  }, [getStorageKey]);

  // Save navigation state to session storage
  const saveNavigationState = useCallback((state: NavigationState) => {
    try {
      sessionStorage.setItem(getStorageKey(state.certificateId), JSON.stringify(state));
    } catch (error) {
      console.error('Error saving navigation state:', error);
    }
  }, [getStorageKey]);

  // Initialize navigation state when certificate changes
  useEffect(() => {
    if (!activeCertificateId) {
      setNavigationState(null);
      return;
    }

    const existingState = loadNavigationState(activeCertificateId);
    if (existingState) {
      setNavigationState(existingState);
    } else {
      // Create new navigation state
      const newState: NavigationState = {
        visitedPages: [],
        highestPageReached: -1,
        certificateId: activeCertificateId,
        timestamp: Date.now()
      };
      setNavigationState(newState);
      saveNavigationState(newState);
    }
  }, [activeCertificateId, loadNavigationState, saveNavigationState]);

  // Mark a page as visited
  const markPageAsVisited = useCallback((pageType: PageType) => {
    // Early return if required dependencies are not available
    if (!certificateType || !activeCertificateId) return;

    const pages = certificateTypePages[certificateType];
    const pageIndex = pages.findIndex(page => page === pageType);

    if (pageIndex === -1) return;

    // Use functional update to avoid dependency on navigationState
    setNavigationState(prevState => {
      // If no previous state, return early
      if (!prevState) return prevState;

      // Check if page is already visited to avoid unnecessary updates
      if (prevState.visitedPages.includes(pageType) && prevState.highestPageReached >= pageIndex) {
        return prevState;
      }

      const updatedState: NavigationState = {
        ...prevState,
        visitedPages: [...new Set([...prevState.visitedPages, pageType])],
        highestPageReached: Math.max(prevState.highestPageReached, pageIndex),
        timestamp: Date.now()
      };

      // Save to storage
      saveNavigationState(updatedState);
      return updatedState;
    });
  }, [certificateType, activeCertificateId, saveNavigationState]);

  // Check if a page is accessible (completed, visited, or current)
  const isPageAccessible = useCallback((pageType: PageType, currentPageType: PageType | null): boolean => {
    // Return false if required dependencies are not available
    if (!navigationState || !certificateType || !currentPageType) return false;

    const pages = certificateTypePages[certificateType];
    const pageIndex = pages.findIndex(page => page === pageType);
    const currentPageIndex = pages.findIndex(page => page === currentPageType);

    if (pageIndex === -1 || currentPageIndex === -1) return false;

    // Current page is always accessible
    if (pageType === currentPageType) return true;

    // Completed pages (before current page) are accessible
    if (pageIndex < currentPageIndex) return true;

    // Previously visited pages are accessible
    if (navigationState.visitedPages.includes(pageType)) return true;

    return false;
  }, [navigationState, certificateType]);

  // Check if a page is completed (before current page)
  const isPageCompleted = useCallback((pageType: PageType, currentPageType: PageType | null): boolean => {
    // Return false if required dependencies are not available
    if (!certificateType || !currentPageType) return false;

    const pages = certificateTypePages[certificateType];
    const pageIndex = pages.findIndex(page => page === pageType);
    const currentPageIndex = pages.findIndex(page => page === currentPageType);

    return pageIndex !== -1 && currentPageIndex !== -1 && pageIndex < currentPageIndex;
  }, [certificateType]);

  // Check if a page is visited but not completed
  const isPageVisited = useCallback((pageType: PageType, currentPageType: PageType | null): boolean => {
    // Return false if required dependencies are not available
    if (!navigationState || !certificateType || !currentPageType) return false;

    const isCompleted = isPageCompleted(pageType, currentPageType);
    const isVisited = navigationState.visitedPages.includes(pageType);

    return isVisited && !isCompleted && pageType !== currentPageType;
  }, [navigationState, certificateType, isPageCompleted]);

  // Clear navigation state (useful when switching certificates)
  const clearNavigationState = useCallback(() => {
    if (activeCertificateId) {
      try {
        sessionStorage.removeItem(getStorageKey(activeCertificateId));
      } catch (error) {
        console.error('Error clearing navigation state:', error);
      }
    }
    setNavigationState(null);
  }, [activeCertificateId, getStorageKey]);

  return {
    navigationState,
    markPageAsVisited,
    isPageAccessible,
    isPageCompleted,
    isPageVisited,
    clearNavigationState
  };
};
