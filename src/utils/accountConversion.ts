import { supabase } from '../lib/supabase';
import { transferCertificateFiles } from './fileUtils';

/**
 * Utility functions for converting anonymous users to permanent accounts
 */

/**
 * Gets the user's email from their certificate data
 */
export const getUserEmailFromCertificate = async (certificateId: string): Promise<string | null> => {
  try {
    const { data, error } = await supabase
      .from('energieausweise')
      .select('objektdaten')
      .eq('id', certificateId)
      .single();

    if (error || !data?.objektdaten) {
      return null;
    }

    const objektdaten = data.objektdaten as any;
    return objektdaten?.Kunden_email || null;
  } catch (error) {
    console.error('Error getting email from certificate:', error);
    return null;
  }
};

/**
 * Converts an anonymous user to a permanent user with email and password
 * Enhanced version that also handles file transfers for certificates
 */
export const convertAnonymousUser = async (
  email: string,
  password: string,
  certificateId?: string
): Promise<{
  success: boolean;
  error?: string;
  fileTransferResult?: {
    transferredFiles?: number;
    failedFiles?: string[];
  };
}> => {
  try {
    // Get the current user ID before conversion
    const { data: currentUser } = await supabase.auth.getUser();
    const anonymousUserId = currentUser.user?.id;

    if (!anonymousUserId) {
      return {
        success: false,
        error: 'Kein aktueller Benutzer gefunden',
      };
    }

    // Perform the account conversion
    const { error } = await supabase.auth.updateUser({
      email,
      password,
    });

    if (error) {
      return {
        success: false,
        error: error.message,
      };
    }

    // Get the updated user ID after conversion
    const { data: updatedUser } = await supabase.auth.getUser();
    const registeredUserId = updatedUser.user?.id;

    if (!registeredUserId) {
      return {
        success: false,
        error: 'Fehler beim Abrufen der neuen Benutzer-ID',
      };
    }

    // If user ID changed during conversion and we have a certificate, transfer files
    let fileTransferResult;
    if (certificateId && anonymousUserId !== registeredUserId) {
      console.log(`🔄 User ID changed during conversion: ${anonymousUserId} -> ${registeredUserId}`);
      console.log('📁 Starting file transfer for account conversion...');

      fileTransferResult = await transferCertificateFiles(
        anonymousUserId,
        registeredUserId,
        certificateId
      );

      if (!fileTransferResult.success) {
        console.error('❌ File transfer failed during account conversion:', fileTransferResult.error);
        // Don't fail the entire conversion for file transfer issues, but log the error
        console.warn('⚠️ Account conversion succeeded but file transfer failed');
      } else {
        console.log(`✅ File transfer completed during account conversion: ${fileTransferResult.transferredFiles || 0} files transferred`);
      }
    }

    return {
      success: true,
      ...(fileTransferResult && { fileTransferResult })
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.message || 'Unbekannter Fehler beim Konvertieren des Kontos',
    };
  }
};

/**
 * Checks if the current user is anonymous
 */
export const isCurrentUserAnonymous = async (): Promise<boolean> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    return user?.is_anonymous ?? false;
  } catch (error) {
    console.error('Error checking if user is anonymous:', error);
    return false;
  }
};

/**
 * Gets the current user's active certificate ID
 */
export const getActiveCertificateId = (): string | null => {
  return localStorage.getItem('activeCertificateId');
};

/**
 * Handles the complete account conversion flow
 */
export const handleAccountConversion = async (password: string): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    // Get the active certificate ID
    const certificateId = getActiveCertificateId();
    if (!certificateId) {
      return {
        success: false,
        error: 'Kein aktives Zertifikat gefunden',
      };
    }

    // Get the user's email from the certificate
    const email = await getUserEmailFromCertificate(certificateId);
    if (!email) {
      return {
        success: false,
        error: 'E-Mail-Adresse nicht in den Zertifikatsdaten gefunden',
      };
    }

    // Convert the anonymous user
    const result = await convertAnonymousUser(email, password);
    return result;
  } catch (error: any) {
    return {
      success: false,
      error: error.message || 'Fehler beim Konvertieren des Kontos',
    };
  }
};

/**
 * Checks if an email already exists in the system (for conflict detection)
 */
export const checkEmailExists = async (email: string): Promise<boolean> => {
  try {
    // Try to sign in with a dummy password to check if email exists
    // This is a workaround since Supabase doesn't provide a direct way to check email existence
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password: 'dummy-password-for-check',
    });

    // If we get an "Invalid login credentials" error, the email exists but password is wrong
    // If we get "Email not confirmed" error, the email exists
    // If we get other errors, we assume email doesn't exist
    if (error) {
      return error.message.includes('Invalid login credentials') ||
             error.message.includes('Email not confirmed') ||
             error.message.includes('Invalid credentials') ||
             error.message.includes('User already registered');
    }

    return true; // If no error, email exists and password was correct (unlikely with dummy password)
  } catch (error) {
    console.error('Error checking email existence:', error);
    return false;
  }
};

/**
 * Enhanced account conversion with email conflict detection and file transfer support
 */
export const convertAnonymousUserWithConflictCheck = async (
  email: string,
  password: string,
  certificateId?: string
): Promise<{
  success: boolean;
  error?: string;
  conflictType?: 'email_exists' | 'conversion_failed';
  fileTransferResult?: {
    transferredFiles?: number;
    failedFiles?: string[];
  };
}> => {
  try {
    // First check if email already exists
    const emailExists = await checkEmailExists(email);

    if (emailExists) {
      return {
        success: false,
        error: 'Diese E-Mail-Adresse ist bereits registriert.',
        conflictType: 'email_exists',
      };
    }

    // Use the enhanced conversion function that handles file transfers
    const conversionResult = await convertAnonymousUser(email, password, certificateId);

    if (!conversionResult.success) {
      // Check for specific Supabase error messages
      if (conversionResult.error?.includes('User already registered') ||
          conversionResult.error?.includes('Email already registered')) {
        return {
          success: false,
          error: 'Diese E-Mail-Adresse ist bereits registriert.',
          conflictType: 'email_exists',
        };
      }

      return {
        success: false,
        error: conversionResult.error,
        conflictType: 'conversion_failed',
      };
    }

    return {
      success: true,
      ...(conversionResult.fileTransferResult && { fileTransferResult: conversionResult.fileTransferResult })
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.message || 'Unbekannter Fehler beim Konvertieren des Kontos',
      conflictType: 'conversion_failed',
    };
  }
};

/**
 * Transfer certificate data from anonymous user to existing account
 * Now includes file storage transfer operations
 */
export const transferCertificateToExistingAccount = async (
  certificateId: string,
  targetUserId: string
): Promise<{
  success: boolean;
  error?: string;
  fileTransferResult?: {
    transferredFiles?: number;
    failedFiles?: string[];
  };
}> => {
  try {
    console.log(`🔄 Starting certificate transfer: ${certificateId} -> ${targetUserId}`);

    // First, get the current certificate to verify it exists and check current ownership
    const { data: currentCert, error: fetchError } = await supabase
      .from('energieausweise')
      .select('user_id, certificate_type, created_at')
      .eq('id', certificateId)
      .single();

    if (fetchError) {
      console.error('❌ Error fetching certificate for transfer:', fetchError);
      return {
        success: false,
        error: 'Zertifikat nicht gefunden.',
      };
    }

    const sourceUserId = currentCert.user_id;
    console.log(`📋 Current certificate owner: ${sourceUserId}`);
    console.log(`🎯 Target user: ${targetUserId}`);

    // If already owned by target user, no transfer needed
    if (sourceUserId === targetUserId) {
      console.log('✅ Certificate already owned by target user, no transfer needed');
      return { success: true };
    }

    // Step 1: Transfer files first (before database transfer)
    // This way if file transfer fails, we don't have orphaned database records
    console.log('📁 Starting file transfer...');
    const fileTransferResult = await transferCertificateFiles(
      sourceUserId,
      targetUserId,
      certificateId
    );

    if (!fileTransferResult.success) {
      console.error('❌ File transfer failed:', fileTransferResult.error);
      return {
        success: false,
        error: `Fehler beim Übertragen der Dateien: ${fileTransferResult.error}`,
        fileTransferResult: {
          transferredFiles: fileTransferResult.transferredFiles,
          failedFiles: fileTransferResult.failedFiles
        }
      };
    }

    console.log(`✅ File transfer completed: ${fileTransferResult.transferredFiles || 0} files transferred`);

    // Step 2: Transfer database ownership
    console.log('🗄️ Starting database transfer...');
    const { data, error } = await supabase.rpc('transfer_certificate_ownership', {
      certificate_id: certificateId,
      new_user_id: targetUserId
    });

    if (error) {
      console.error('❌ RPC transfer failed:', error);

      // If database transfer fails after successful file transfer, we should attempt rollback
      console.log('🔄 Attempting to rollback file transfer...');
      const rollbackResult = await transferCertificateFiles(
        targetUserId,
        sourceUserId,
        certificateId
      );

      if (!rollbackResult.success) {
        console.error('❌ File rollback also failed:', rollbackResult.error);
        return {
          success: false,
          error: 'Fehler beim Übertragen der Zertifikatsdaten. Dateien konnten nicht zurückgesetzt werden.',
          fileTransferResult: {
            transferredFiles: fileTransferResult.transferredFiles,
            failedFiles: fileTransferResult.failedFiles
          }
        };
      }

      console.log('✅ File rollback completed');
      return {
        success: false,
        error: 'Fehler beim Übertragen der Zertifikatsdaten.',
      };
    }

    // Parse the JSON response from the RPC function
    const result = data as { success: boolean; error?: string; message?: string };

    if (!result.success) {
      console.error('❌ RPC function returned error:', result.error);

      // Attempt rollback
      console.log('🔄 Attempting to rollback file transfer...');
      await transferCertificateFiles(targetUserId, sourceUserId, certificateId);

      return {
        success: false,
        error: result.error || 'Fehler beim Übertragen der Zertifikatsdaten.',
      };
    }

    console.log('✅ RPC transfer succeeded:', result);

    // Step 3: Verify the transfer was successful
    const { data: verifyData, error: verifyError } = await supabase
      .from('energieausweise')
      .select('user_id')
      .eq('id', certificateId)
      .single();

    if (verifyError || verifyData.user_id !== targetUserId) {
      console.error('❌ Transfer verification failed:', verifyError || 'User ID mismatch');

      // Attempt rollback
      console.log('🔄 Attempting to rollback file transfer...');
      await transferCertificateFiles(targetUserId, sourceUserId, certificateId);

      return {
        success: false,
        error: 'Übertragung konnte nicht verifiziert werden.',
      };
    }

    console.log('✅ Certificate and file transfer completed and verified successfully');
    return {
      success: true,
      fileTransferResult: {
        transferredFiles: fileTransferResult.transferredFiles,
        failedFiles: fileTransferResult.failedFiles
      }
    };
  } catch (error: any) {
    console.error('❌ Unexpected error during certificate transfer:', error);
    return {
      success: false,
      error: error.message || 'Unbekannter Fehler beim Übertragen der Daten',
    };
  }
};
