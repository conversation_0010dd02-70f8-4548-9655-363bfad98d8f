import { supabase } from '../lib/supabase';
import { transferCertificateFiles, listCertificateFiles } from './fileUtils';

/**
 * Test utility to verify file transfer functionality
 * This is for development/testing purposes only
 */

export interface FileTransferTestResult {
  success: boolean;
  error?: string;
  details: {
    sourceFiles: number;
    targetFiles: number;
    transferredFiles: number;
    failedFiles?: string[];
  };
}

/**
 * Test file transfer between two user directories
 * @param fromUserId Source user ID
 * @param toUserId Target user ID  
 * @param certificateId Certificate ID
 * @returns Test results
 */
export const testFileTransfer = async (
  fromUserId: string,
  toUserId: string,
  certificateId: string
): Promise<FileTransferTestResult> => {
  try {
    console.log(`🧪 Testing file transfer: ${fromUserId}/${certificateId} -> ${toUserId}/${certificateId}`);

    // List files before transfer
    const sourceFilesBefore = await listCertificateFiles(fromUserId, certificateId);
    const targetFilesBefore = await listCertificateFiles(toUserId, certificateId);

    console.log(`📊 Before transfer - Source: ${sourceFilesBefore.length} files, Target: ${targetFilesBefore.length} files`);

    // Perform the transfer
    const transferResult = await transferCertificateFiles(fromUserId, toUserId, certificateId);

    if (!transferResult.success) {
      return {
        success: false,
        error: transferResult.error,
        details: {
          sourceFiles: sourceFilesBefore.length,
          targetFiles: targetFilesBefore.length,
          transferredFiles: transferResult.transferredFiles || 0,
          failedFiles: transferResult.failedFiles
        }
      };
    }

    // List files after transfer
    const sourceFilesAfter = await listCertificateFiles(fromUserId, certificateId);
    const targetFilesAfter = await listCertificateFiles(toUserId, certificateId);

    console.log(`📊 After transfer - Source: ${sourceFilesAfter.length} files, Target: ${targetFilesAfter.length} files`);

    const expectedTargetFiles = targetFilesBefore.length + (transferResult.transferredFiles || 0);
    const actualTargetFiles = targetFilesAfter.length;

    if (actualTargetFiles !== expectedTargetFiles) {
      return {
        success: false,
        error: `File count mismatch: expected ${expectedTargetFiles} target files, got ${actualTargetFiles}`,
        details: {
          sourceFiles: sourceFilesBefore.length,
          targetFiles: targetFilesAfter.length,
          transferredFiles: transferResult.transferredFiles || 0,
          failedFiles: transferResult.failedFiles
        }
      };
    }

    return {
      success: true,
      details: {
        sourceFiles: sourceFilesBefore.length,
        targetFiles: targetFilesAfter.length,
        transferredFiles: transferResult.transferredFiles || 0,
        failedFiles: transferResult.failedFiles
      }
    };

  } catch (error: any) {
    console.error('❌ Test error:', error);
    return {
      success: false,
      error: error.message || 'Unknown test error',
      details: {
        sourceFiles: 0,
        targetFiles: 0,
        transferredFiles: 0
      }
    };
  }
};

/**
 * Verify that files exist and are accessible after transfer
 * @param userId User ID to check
 * @param certificateId Certificate ID
 * @returns Verification results
 */
export const verifyFilesAccessible = async (
  userId: string,
  certificateId: string
): Promise<{
  success: boolean;
  error?: string;
  accessibleFiles: number;
  inaccessibleFiles: string[];
}> => {
  try {
    const files = await listCertificateFiles(userId, certificateId);
    const inaccessibleFiles: string[] = [];

    // Try to get signed URLs for each file to verify accessibility
    for (const file of files) {
      try {
        const { data, error } = await supabase.storage
          .from('certificateuploads')
          .createSignedUrl(file.path, 60);

        if (error || !data?.signedUrl) {
          inaccessibleFiles.push(file.name);
        }
      } catch (error) {
        inaccessibleFiles.push(file.name);
      }
    }

    return {
      success: inaccessibleFiles.length === 0,
      accessibleFiles: files.length - inaccessibleFiles.length,
      inaccessibleFiles,
      ...(inaccessibleFiles.length > 0 && {
        error: `${inaccessibleFiles.length} files are not accessible: ${inaccessibleFiles.join(', ')}`
      })
    };

  } catch (error: any) {
    return {
      success: false,
      error: error.message || 'Unknown verification error',
      accessibleFiles: 0,
      inaccessibleFiles: []
    };
  }
};

/**
 * Log file transfer test results in a readable format
 * @param result Test result to log
 */
export const logTestResults = (result: FileTransferTestResult) => {
  console.log('\n🧪 FILE TRANSFER TEST RESULTS');
  console.log('================================');
  console.log(`Status: ${result.success ? '✅ SUCCESS' : '❌ FAILED'}`);
  
  if (result.error) {
    console.log(`Error: ${result.error}`);
  }
  
  console.log(`Source files: ${result.details.sourceFiles}`);
  console.log(`Target files: ${result.details.targetFiles}`);
  console.log(`Transferred files: ${result.details.transferredFiles}`);
  
  if (result.details.failedFiles && result.details.failedFiles.length > 0) {
    console.log(`Failed files: ${result.details.failedFiles.join(', ')}`);
  }
  
  console.log('================================\n');
};
