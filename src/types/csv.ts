// CSV export types and interfaces for energy certificate export

export interface CsvFieldMapping {
  csvColumn: string;
  dbPath: string; // Path to the field in the database (e.g., 'objektdaten.PLZ' or 'gebaeudedetails1.Baujahr')
  certificateTypes: ('WG/V' | 'WG/B' | 'NWG/V')[]; // Which certificate types this field applies to
  defaultValue?: string | number;
  transform?: (value: any) => string; // Optional transformation function
}

export interface EnergieausweisData {
  id: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  objektdaten: Record<string, any> | null;
  gebaeudedetails1: Record<string, any> | null;
  gebaeudedetails2: Record<string, any> | null;
  fenster: Record<string, any> | null;
  heizung: Record<string, any> | null;
  trinkwarmwasser: Record<string, any> | null;
  lueftung: Record<string, any> | null;
  verbrauchsdaten: Record<string, any> | null;
  payment_status: string | null;
  status: string | null;
  certificate_type: string | null;
  stripe_checkout_session_id: string | null;
  order_number: string | null;
}
