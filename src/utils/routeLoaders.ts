import { redirect } from '@tanstack/react-router';
import { supabase } from '../lib/supabase';
import type { Session, User } from '@supabase/supabase-js';

// Define the router context type
export type RouterContext = {
  user: User | null;
  session: Session | null;
  loading: boolean;
};

// Cache for certificate type to reduce database calls
interface CertificateTypeCache {
  userId: string | null;
  certificateType: string | null;
  timestamp: number;
}

// Cache expiration time in milliseconds (5 minutes)
const CACHE_EXPIRATION = 5 * 60 * 1000;

// In-memory cache for certificate type
let certificateTypeCache: CertificateTypeCache = {
  userId: null,
  certificateType: null,
  timestamp: 0,
};

/**
 * Check if the user is authenticated and redirect to login if not
 */
/**
 * Check if the user is authenticated and redirect to login if not.
 * Suitable for use as a Tanstack Router loader.
 */
export async function checkAuth(redirectTo: string) {
  const { data: { session } } = await supabase.auth.getSession();

  if (!session) {
    throw redirect({
      to: '/login',
      search: {
        redirect: redirectTo,
      },
    });
  }
  // Return session and user for use in route components or other loaders
  return { session, user: session.user };
}

/**
 * Check if the user is authenticated (including anonymous users) for certificate data entry routes.
 * Allows both permanent and anonymous users to access certificate creation pages.
 */
export async function checkAuthOrAnonymous(redirectTo: string) {
  const { data: { session } } = await supabase.auth.getSession();

  if (!session) {
    throw redirect({
      to: '/login',
      search: {
        redirect: redirectTo,
      },
    });
  }
  // Return session and user for use in route components or other loaders
  return { session, user: session.user };
}

/**
 * Fetch the certificate type for the current user with caching
 */
export async function getCertificateType(): Promise<string | null> {
  try {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) return null;

    const userId = user.user.id;
    const now = Date.now();

    // Check if we have a valid cached value
    if (
      certificateTypeCache.userId === userId &&
      certificateTypeCache.certificateType !== null &&
      now - certificateTypeCache.timestamp < CACHE_EXPIRATION
    ) {
      return certificateTypeCache.certificateType;
    }

    // Get the active certificate ID from localStorage
    const activeCertificateId = localStorage.getItem('activeCertificateId');
    
    if (!activeCertificateId) return null;

    // Fetch the specific certificate by ID
    const { data, error } = await supabase
      .from('energieausweise')
      .select('certificate_type')
      .eq('id', activeCertificateId)
      .single();

    if (error) {
      console.error('Error fetching certificate:', error);
      return null;
    }

    // Update cache
    certificateTypeCache = {
      userId,
      certificateType: data?.certificate_type || null,
      timestamp: now,
    };

    return data?.certificate_type || null;
  } catch (error) {
    console.error('Error fetching certificate type:', error);
    return null;
  }
}

/**
 * Fetch the user's role directly from Supabase
 */
export async function getUserRole(): Promise<string | null> {
  try {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) return null;

    const { data, error } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.user.id)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 is "No rows found"
      throw error;
    }

    return data?.role || null;
  } catch (error) {
    console.error('Error fetching user role in loader:', error);
    return null;
  }
}

/**
 * Check if the certificate type matches the required type and redirect if not
 */
export async function checkCertificateType({
  requiredType,
  redirectTo,
}: {
  requiredType: string;
  redirectTo: string;
}): Promise<void> {
  try {
    const certificateType = await getCertificateType();
    
    // Only redirect if certificate type is not null and doesn't match required type
    if (certificateType !== null && certificateType !== requiredType) {
      throw redirect({
        to: redirectTo,
      });
    }
  } catch (error) {
    if (error instanceof Error && 'to' in error) {
      throw error; // Re-throw redirect errors
    }
    console.error('Error checking certificate type:', error);
    // If there's an error, redirect to the fallback page
    throw redirect({
      to: redirectTo,
    });
  }
}

/**
 * Check if the certificate type is one of the required types and redirect if not
 */
export async function checkCertificateTypes({
  requiredTypes,
  redirectTo,
}: {
  requiredTypes: string[];
  redirectTo: string;
}): Promise<void> {
  try {
    const certificateType = await getCertificateType();
    
    if (!certificateType || !requiredTypes.includes(certificateType)) {
      throw redirect({
        to: redirectTo,
      });
    }
  } catch (error) {
    if (error instanceof Error && 'to' in error) {
      throw error; // Re-throw redirect errors
    }
    console.error('Error checking certificate type:', error);
    // If there's an error, redirect to the fallback page
    throw redirect({
      to: redirectTo,
    });
  }
}
