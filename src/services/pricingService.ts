/**
 * Pricing Service for Energy Certificate Application
 * 
 * This service handles all pricing-related operations including:
 * - Fetching current prices for certificate types
 * - Managing Stripe integration
 * - Providing type-safe pricing operations
 * - Admin pricing management
 */

import { supabase } from '../lib/supabase';
import type { CertificateType } from '../utils/certificateTypeMapping';

export interface CertificatePricing {
  id: string;
  certificate_type: CertificateType;
  price_cents: number;
  currency: string;
  stripe_product_id: string | null;
  stripe_price_id: string | null;
  is_active: boolean;
  display_name: string;
  description: string | null;
  created_at: string;
  updated_at: string;
}

export interface PricingDisplayInfo {
  certificate_type: CertificateType;
  price_euros: string;
  price_cents: number;
  display_name: string;
  description: string;
  stripe_price_id: string | null;
}

export class PricingService {
  /**
   * Get active pricing for all certificate types
   */
  static async getAllActivePricing(): Promise<CertificatePricing[]> {
    const { data, error } = await supabase
      .from('certificate_pricing')
      .select('*')
      .eq('is_active', true)
      .order('certificate_type');

    if (error) {
      console.error('Error fetching pricing:', error);
      throw new Error('<PERSON><PERSON> beim <PERSON>');
    }

    return (data || []) as CertificatePricing[];
  }

  /**
   * Get pricing for a specific certificate type
   */
  static async getPricingForType(certificateType: CertificateType): Promise<CertificatePricing | null> {
    const { data, error } = await supabase
      .from('certificate_pricing')
      .select('*')
      .eq('certificate_type', certificateType)
      .eq('is_active', true)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No rows returned
        return null;
      }
      console.error('Error fetching pricing for type:', certificateType, error);
      throw new Error(`Fehler beim Laden des Preises für ${certificateType}`);
    }

    return data as CertificatePricing;
  }

  /**
   * Get pricing display information for frontend components
   */
  static async getPricingDisplayInfo(): Promise<PricingDisplayInfo[]> {
    const pricing = await this.getAllActivePricing();
    
    return pricing.map(p => ({
      certificate_type: p.certificate_type,
      price_euros: this.formatPriceEuros(p.price_cents),
      price_cents: p.price_cents,
      display_name: p.display_name,
      description: p.description || '',
      stripe_price_id: p.stripe_price_id
    }));
  }

  /**
   * Get pricing display info for a specific certificate type
   */
  static async getPricingDisplayInfoForType(certificateType: CertificateType): Promise<PricingDisplayInfo | null> {
    const pricing = await this.getPricingForType(certificateType);
    
    if (!pricing) {
      return null;
    }

    return {
      certificate_type: pricing.certificate_type,
      price_euros: this.formatPriceEuros(pricing.price_cents),
      price_cents: pricing.price_cents,
      display_name: pricing.display_name,
      description: pricing.description || '',
      stripe_price_id: pricing.stripe_price_id
    };
  }

  /**
   * Format price in cents to euros string (e.g., 4900 -> "49,00 €")
   */
  static formatPriceEuros(priceCents: number): string {
    const euros = priceCents / 100;
    return `${euros.toFixed(2).replace('.', ',')} €`;
  }

  /**
   * Admin function: Update pricing for a certificate type
   */
  static async updatePricing(
    certificateType: CertificateType,
    priceCents: number,
    stripeProductId?: string,
    stripePriceId?: string
  ): Promise<CertificatePricing> {
    const updateData: Partial<CertificatePricing> = {
      price_cents: priceCents,
      updated_at: new Date().toISOString()
    };

    if (stripeProductId !== undefined) {
      updateData.stripe_product_id = stripeProductId;
    }
    if (stripePriceId !== undefined) {
      updateData.stripe_price_id = stripePriceId;
    }

    const { data, error } = await supabase
      .from('certificate_pricing')
      .update(updateData)
      .eq('certificate_type', certificateType)
      .eq('is_active', true)
      .select()
      .single();

    if (error) {
      console.error('Error updating pricing:', error);
      throw new Error(`Fehler beim Aktualisieren des Preises für ${certificateType}`);
    }

    return data as CertificatePricing;
  }

  /**
   * Admin function: Get all pricing records (including inactive)
   */
  static async getAllPricingRecords(): Promise<CertificatePricing[]> {
    const { data, error } = await supabase
      .from('certificate_pricing')
      .select('*')
      .order('certificate_type', { ascending: true })
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching all pricing records:', error);
      throw new Error('Fehler beim Laden aller Preisdatensätze');
    }

    return (data || []) as CertificatePricing[];
  }

  /**
   * Validate that all certificate types have active pricing
   */
  static async validatePricingCompleteness(): Promise<{
    isComplete: boolean;
    missingTypes: CertificateType[];
  }> {
    const allTypes: CertificateType[] = ['WG/V', 'WG/B', 'NWG/V'];
    const activePricing = await this.getAllActivePricing();
    const activePricingTypes = activePricing.map(p => p.certificate_type);
    
    const missingTypes = allTypes.filter(type => !activePricingTypes.includes(type));
    
    return {
      isComplete: missingTypes.length === 0,
      missingTypes
    };
  }

  /**
   * Get default fallback pricing (for error scenarios)
   */
  static getDefaultPricing(): PricingDisplayInfo[] {
    return [
      {
        certificate_type: 'WG/V',
        price_euros: '49,00 €',
        price_cents: 4900,
        display_name: 'Wohngebäude-Verbrauchsausweis',
        description: 'Für Wohngebäude basierend auf dem tatsächlichen Energieverbrauch',
        stripe_price_id: null
      },
      {
        certificate_type: 'WG/B',
        price_euros: '49,00 €',
        price_cents: 4900,
        display_name: 'Wohngebäude-Bedarfsausweis',
        description: 'Für Wohngebäude basierend auf dem berechneten Energiebedarf',
        stripe_price_id: null
      },
      {
        certificate_type: 'NWG/V',
        price_euros: '49,00 €',
        price_cents: 4900,
        display_name: 'Nicht-Wohngebäude-Verbrauchsausweis',
        description: 'Für Nicht-Wohngebäude basierend auf dem tatsächlichen Energieverbrauch',
        stripe_price_id: null
      }
    ];
  }
}
