-- Create a function to transfer certificate ownership that bypasses RLS
-- This is needed because RLS policies prevent users from updating certificates they don't own
-- but we need to transfer ownership from anonymous users to registered users

CREATE OR REPLACE FUNCTION transfer_certificate_ownership(
  certificate_id UUID,
  new_user_id UUID
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER -- This allows the function to bypass RLS
AS $$
DECLARE
  result JSON;
  old_user_id UUID;
  rows_affected INTEGER;
BEGIN
  -- Get the current owner for logging
  SELECT user_id INTO old_user_id 
  FROM energieausweise 
  WHERE id = certificate_id;
  
  -- Check if certificate exists
  IF old_user_id IS NULL THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Certificate not found'
    );
  END IF;
  
  -- Check if already owned by target user
  IF old_user_id = new_user_id THEN
    RETURN json_build_object(
      'success', true,
      'message', 'Certificate already owned by target user'
    );
  END IF;
  
  -- Perform the transfer
  UPDATE energieausweise 
  SET 
    user_id = new_user_id,
    updated_at = NOW()
  WHERE id = certificate_id;
  
  GET DIAGNOSTICS rows_affected = ROW_COUNT;
  
  -- Check if update was successful
  IF rows_affected = 0 THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Failed to update certificate ownership'
    );
  END IF;
  
  -- Return success with details
  RETURN json_build_object(
    'success', true,
    'certificate_id', certificate_id,
    'old_user_id', old_user_id,
    'new_user_id', new_user_id,
    'transferred_at', NOW()
  );
  
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', SQLERRM
    );
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION transfer_certificate_ownership(UUID, UUID) TO authenticated;

-- Add a comment explaining the function
COMMENT ON FUNCTION transfer_certificate_ownership(UUID, UUID) IS 
'Transfers ownership of a certificate from one user to another. Uses SECURITY DEFINER to bypass RLS policies. Used primarily for transferring certificates from anonymous users to registered users during account conversion.';
