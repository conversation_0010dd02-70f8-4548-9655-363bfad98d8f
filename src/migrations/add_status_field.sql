-- Add status field to energieausweise table
ALTER TABLE energieausweise 
ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'in_progress';

-- Update existing records to have a status
UPDATE energieausweise 
SET status = CASE 
  WHEN payment_status = 'paid' THEN 'paid'
  WHEN payment_status IS NOT NULL THEN 'completed'
  ELSE 'in_progress'
END;

-- Remove the onConflict constraint on user_id
-- This is done by creating a new unique index that replaces the old one
-- First, drop any existing unique constraint on user_id
ALTER TABLE energieausweise 
DROP CONSTRAINT IF EXISTS energieausweise_user_id_key;

-- Create a comment to explain the change
COMMENT ON TABLE energieausweise IS 'Table for storing energy certificate data. Multiple certificates per user are now supported.';
