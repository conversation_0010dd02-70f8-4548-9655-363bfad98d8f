# Deployment and Operations Guide

## Pre-Deployment Checklist

### Environment Preparation

- [ ] Supabase project configured with required environment variables
- [ ] Stripe account with webhook endpoint configured
- [ ] Resend account for email notifications (optional)
- [ ] Database backup completed
- [ ] Test environment validated

### Required Environment Variables

```bash
# Stripe Configuration
STRIPE_SECRET_KEY=sk_live_... # or sk_test_... for testing
STRIPE_WEBHOOK_SECRET=whsec_...

# Email Service (Optional)
RESEND_API_KEY=re_...

# Supabase (Auto-configured)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJ...
```

## Deployment Steps

### 1. Database Migrations

Execute migrations in the following order:

```bash
# Step 1: Add payment status values and indexes
supabase db push --file src/migrations/add_payment_status_values.sql

# Step 2: Create payment attempts table
supabase db push --file src/migrations/create_payment_attempts_table.sql

# Step 3: Create analytics function
supabase db push --file src/migrations/create_payment_analytics_function.sql
```

#### Verification Queries

```sql
-- Verify new payment statuses are supported
SELECT DISTINCT payment_status FROM energieausweise;

-- Verify payment_attempts table exists
\d payment_attempts

-- Test analytics function
SELECT get_payment_analytics();
```

### 2. Edge Functions Deployment

```bash
# Deploy enhanced webhook handler
supabase functions deploy stripe-webhook

# Deploy new cancellation logging function
supabase functions deploy log-payment-cancellation

# Deploy session monitoring function
supabase functions deploy check-abandoned-sessions
```

#### Function Verification

```bash
# Test webhook endpoint
curl -X POST https://your-project.supabase.co/functions/v1/stripe-webhook \
  -H "Content-Type: application/json" \
  -H "Stripe-Signature: test" \
  -d '{"test": true}'

# Test cancellation logging
curl -X POST https://your-project.supabase.co/functions/v1/log-payment-cancellation \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ANON_KEY" \
  -d '{"reason": "test", "timestamp": "2024-01-01T00:00:00Z"}'

# Test session monitoring
curl -X POST https://your-project.supabase.co/functions/v1/check-abandoned-sessions \
  -H "Authorization: Bearer YOUR_SERVICE_KEY"
```

### 3. Frontend Deployment

```bash
# Install dependencies
npm install

# Build application
npm run build

# Deploy to hosting platform
npm run deploy
```

### 4. Stripe Webhook Configuration

#### Webhook Endpoint Setup

1. Navigate to Stripe Dashboard → Webhooks
2. Click "Add endpoint"
3. Enter endpoint URL: `https://your-project.supabase.co/functions/v1/stripe-webhook`
4. Select events to send:

```
checkout.session.completed
checkout.session.expired
checkout.session.async_payment_failed
checkout.session.async_payment_succeeded
payment_intent.succeeded
payment_intent.payment_failed
charge.failed
charge.dispute.created
invoice.payment_failed
payment_method.automatically_updated
```

5. Copy webhook signing secret to environment variables

#### Testing Webhook Delivery

```bash
# Use Stripe CLI for local testing
stripe listen --forward-to localhost:54321/functions/v1/stripe-webhook

# Trigger test events
stripe trigger checkout.session.completed
stripe trigger checkout.session.expired
```

## Post-Deployment Verification

### 1. End-to-End Payment Flow Test

```bash
# Test complete payment flow
1. Create test certificate
2. Initiate payment
3. Complete payment in Stripe test mode
4. Verify webhook processing
5. Check database updates
6. Confirm email notifications
```

### 2. Error Handling Test

```bash
# Test cancellation flow
1. Create test certificate
2. Initiate payment
3. Cancel payment (click back)
4. Verify cancellation logging
5. Check error messaging
```

### 3. Admin Dashboard Test

```bash
# Verify admin functionality
1. Access admin dashboard
2. Check analytics display
3. Test auto-refresh
4. Verify webhook events table
5. Test manual refresh
```

## Monitoring and Alerting Setup

### Key Metrics to Monitor

#### Application Metrics

```sql
-- Payment conversion rate (target: >80%)
SELECT 
  ROUND(
    (COUNT(*) FILTER (WHERE payment_status = 'paid')::FLOAT / 
     COUNT(*)::FLOAT) * 100, 2
  ) as conversion_rate
FROM energieausweise
WHERE created_at > NOW() - INTERVAL '24 hours';

-- Webhook processing success rate (target: >99%)
SELECT 
  event_type,
  ROUND(
    (COUNT(*) FILTER (WHERE processing_status = 'handled')::FLOAT / 
     COUNT(*)::FLOAT) * 100, 2
  ) as success_rate
FROM stripe_webhook_events
WHERE created_at > NOW() - INTERVAL '1 hour'
GROUP BY event_type;

-- Average session duration
SELECT 
  AVG(session_duration_seconds) as avg_duration_seconds
FROM payment_attempts
WHERE completed_at > NOW() - INTERVAL '24 hours';
```

#### System Health Checks

```bash
# Function health check
curl -f https://your-project.supabase.co/functions/v1/stripe-webhook/health

# Database connection check
psql -h your-db-host -c "SELECT 1"

# Stripe API connectivity
curl -f https://api.stripe.com/v1/events/latest \
  -H "Authorization: Bearer $STRIPE_SECRET_KEY"
```

### Alerting Configuration

#### Recommended Alerts

```yaml
# Example alerting rules (adapt to your monitoring system)
alerts:
  - name: webhook_processing_failure_rate
    condition: webhook_success_rate < 95%
    window: 1h
    severity: warning
    
  - name: payment_conversion_drop
    condition: conversion_rate < 70%
    window: 4h
    severity: critical
    
  - name: function_execution_time
    condition: avg_execution_time > 30s
    window: 15m
    severity: warning
    
  - name: database_connection_errors
    condition: db_error_rate > 1%
    window: 5m
    severity: critical
```

### Log Monitoring

#### Supabase Function Logs

```bash
# Monitor webhook processing
supabase functions logs stripe-webhook --follow

# Filter for errors
supabase functions logs stripe-webhook --filter "ERROR"

# Monitor specific time range
supabase functions logs stripe-webhook --since "2024-01-01T00:00:00Z"
```

#### Database Query Monitoring

```sql
-- Monitor slow queries
SELECT 
  query,
  mean_exec_time,
  calls
FROM pg_stat_statements
WHERE mean_exec_time > 1000
ORDER BY mean_exec_time DESC;

-- Monitor connection usage
SELECT 
  state,
  COUNT(*) as connection_count
FROM pg_stat_activity
GROUP BY state;
```

## Backup and Recovery

### Database Backup Strategy

```bash
# Daily automated backups (configure in Supabase dashboard)
# Point-in-time recovery available for 7 days

# Manual backup before major changes
pg_dump -h your-db-host -U postgres your-db > backup_$(date +%Y%m%d).sql
```

### Function Code Backup

```bash
# Version control all function code
git tag -a v1.0.0 -m "Webhook enhancements release"
git push origin v1.0.0

# Backup function deployments
supabase functions download stripe-webhook
supabase functions download log-payment-cancellation
supabase functions download check-abandoned-sessions
```

### Recovery Procedures

#### Database Recovery

```bash
# Restore from backup
psql -h your-db-host -U postgres your-db < backup_20240101.sql

# Point-in-time recovery (via Supabase dashboard)
# Navigate to Database → Backups → Restore
```

#### Function Recovery

```bash
# Redeploy functions from backup
supabase functions deploy stripe-webhook
supabase functions deploy log-payment-cancellation
supabase functions deploy check-abandoned-sessions
```

## Performance Optimization

### Database Optimization

#### Index Monitoring

```sql
-- Check index usage
SELECT 
  schemaname,
  tablename,
  indexname,
  idx_scan,
  idx_tup_read,
  idx_tup_fetch
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;

-- Identify missing indexes
SELECT 
  schemaname,
  tablename,
  seq_scan,
  seq_tup_read,
  seq_scan / seq_tup_read as ratio
FROM pg_stat_user_tables
WHERE seq_scan > 0
ORDER BY ratio DESC;
```

#### Query Optimization

```sql
-- Optimize payment analytics query
EXPLAIN ANALYZE SELECT get_payment_analytics();

-- Optimize webhook events query
EXPLAIN ANALYZE 
SELECT event_type, processing_status, COUNT(*)
FROM stripe_webhook_events
WHERE created_at > NOW() - INTERVAL '7 days'
GROUP BY event_type, processing_status;
```

### Function Performance

#### Memory Usage Optimization

```typescript
// Optimize webhook handler memory usage
const processWebhookEvent = async (event: Stripe.Event) => {
  // Process in chunks for large datasets
  const BATCH_SIZE = 100;
  
  // Use streaming for large responses
  const stream = new ReadableStream({
    start(controller) {
      // Stream processing logic
    }
  });
  
  return new Response(stream);
};
```

#### Execution Time Optimization

```typescript
// Parallel processing for independent operations
const processEvent = async (event: Stripe.Event) => {
  const [
    logResult,
    emailResult,
    statusResult
  ] = await Promise.allSettled([
    logEventToDatabase(event),
    sendNotificationEmails(event),
    updatePaymentStatus(event)
  ]);
  
  // Handle results
};
```

## Security Considerations

### Webhook Security

```typescript
// Verify webhook signatures
const verifyWebhookSignature = (body: string, signature: string) => {
  try {
    return stripe.webhooks.constructEvent(body, signature, webhookSecret);
  } catch (err) {
    throw new Error('Invalid webhook signature');
  }
};
```

### Database Security

```sql
-- Verify RLS policies are active
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public';

-- Check policy effectiveness
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual
FROM pg_policies
WHERE schemaname = 'public';
```

### API Security

```typescript
// Rate limiting implementation
const rateLimiter = new Map();

const checkRateLimit = (userId: string, limit: number = 100) => {
  const now = Date.now();
  const userRequests = rateLimiter.get(userId) || [];
  
  // Clean old requests
  const recentRequests = userRequests.filter(
    (timestamp: number) => now - timestamp < 60000
  );
  
  if (recentRequests.length >= limit) {
    throw new Error('Rate limit exceeded');
  }
  
  recentRequests.push(now);
  rateLimiter.set(userId, recentRequests);
};
```

## Maintenance Procedures

### Regular Maintenance Tasks

#### Weekly Tasks

```bash
# Clean up old webhook events (keep 30 days)
DELETE FROM stripe_webhook_events 
WHERE created_at < NOW() - INTERVAL '30 days';

# Clean up old payment attempts (keep 90 days)
DELETE FROM payment_attempts 
WHERE created_at < NOW() - INTERVAL '90 days';

# Analyze table statistics
ANALYZE stripe_webhook_events;
ANALYZE payment_attempts;
ANALYZE energieausweise;
```

#### Monthly Tasks

```bash
# Review and optimize slow queries
SELECT query, mean_exec_time, calls 
FROM pg_stat_statements 
WHERE mean_exec_time > 1000;

# Check database size and growth
SELECT 
  schemaname,
  tablename,
  pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

# Review error logs and patterns
SELECT 
  error_message,
  COUNT(*) as error_count
FROM stripe_webhook_events 
WHERE processing_status = 'error'
AND created_at > NOW() - INTERVAL '30 days'
GROUP BY error_message
ORDER BY error_count DESC;
```

### Scaling Considerations

#### Horizontal Scaling

- Consider read replicas for analytics queries
- Implement connection pooling for high concurrency
- Use CDN for static assets

#### Vertical Scaling

- Monitor CPU and memory usage
- Scale database resources based on query performance
- Optimize function memory allocation

This deployment and operations guide provides comprehensive instructions for successfully deploying and maintaining the enhanced Stripe webhook system. Regular monitoring and maintenance will ensure optimal performance and reliability.
