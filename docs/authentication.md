# Authentifizierung mit Supabase

Dieses Dokument beschreibt die Implementierung der Authentifizierung in der Verbrauchsausweis-App mit Supabase Auth.

## Inhaltsverzeichnis

1. [Überblick](#überblick)
2. [Technische Implementierung](#technische-implementierung)
   - [Supabase-Konfiguration](#supabase-konfiguration)
   - [Auth Context](#auth-context)
   - [Geschützte Routen](#geschützte-routen)
3. [Authentifizierungsflüsse](#authentifizierungsflüsse)
   - [Registrierung](#registrierung)
   - [Anmeldung](#anmeldung)
   - [Passwort zurücksetzen](#passwort-zurücksetzen)
   - [Abmeldung](#abmeldung)
4. [Sicherheitsaspekte](#sicherheitsaspekte)
5. [Benutzerrollen und Berechtigungen](#benutzerrollen-und-berechtigungen)

## Überblick

Die Verbrauchsausweis-App verwendet Supabase Auth für die Benutzerauthentifizierung. Supabase bietet eine vollständige Authentifizierungslösung, die E-Mail/Passwort-Authentifizierung, Passwort-Zurücksetzung und Session-Management umfasst. Die Implementierung nutzt den React Context API, um Authentifizierungsfunktionen und Benutzerdaten in der gesamten Anwendung verfügbar zu machen.

## Technische Implementierung

### Supabase-Konfiguration

Die Supabase-Konfiguration erfolgt in der Datei `src/lib/supabase.ts`. Hier wird der Supabase-Client initialisiert und konfiguriert:

```typescript
// Erstellen des Supabase-Clients
supabaseInstance = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce',
  },
});
```

Wichtige Konfigurationsoptionen:

- **autoRefreshToken**: Automatische Aktualisierung des Tokens, wenn es abläuft
- **persistSession**: Speichert die Sitzung im lokalen Speicher des Browsers
- **detectSessionInUrl**: Erkennt Authentifizierungsparameter in der URL (wichtig für Passwort-Zurücksetzung)
- **flowType**: Verwendet PKCE (Proof Key for Code Exchange) für erhöhte Sicherheit

Die Anwendung verwendet Umgebungsvariablen für die Supabase-URL und den anonymen API-Schlüssel:

- `VITE_SUPABASE_URL`: Die URL der Supabase-Instanz
- `VITE_SUPABASE_ANON_KEY`: Der anonyme API-Schlüssel für öffentliche Operationen

### Auth Context

Der Auth Context (`src/contexts/AuthContext.tsx`) ist das Herzstück der Authentifizierungsimplementierung. Er stellt Authentifizierungsfunktionen und Benutzerdaten für die gesamte Anwendung bereit:

```typescript
export function AuthProvider({ children }: { children: ReactNode }) {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Initiale Sitzung abrufen
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
    });

    // Auf Authentifizierungsänderungen hören
    const { data } = supabase.auth.onAuthStateChange(
      (_event, session) => {
        setSession(session);
        setUser(session?.user ?? null);
        setLoading(false);
      }
    );

    return () => {
      data?.subscription.unsubscribe();
    };
  }, []);

  // Authentifizierungsfunktionen
  const signIn = (email: string, password: string) => {
    return supabase.auth.signInWithPassword({ email, password });
  };

  const signUp = (email: string, password: string) => {
    return supabase.auth.signUp({ email, password });
  };

  const signOut = async () => {
    await supabase.auth.signOut();
  };

  const resetPassword = async (email: string) => {
    const redirectTo = `${getAuthRedirectUrl()}reset-password`;
    return supabase.auth.resetPasswordForEmail(email, {
      redirectTo,
    });
  };

  const updatePassword = async (newPassword: string) => {
    return supabase.auth.updateUser({
      password: newPassword,
    });
  };

  // Bereitstellung des Kontexts
  const value = {
    session,
    user,
    loading,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updatePassword,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}
```

Der Auth Context bietet folgende Funktionen:

- **signIn**: Anmeldung mit E-Mail und Passwort
- **signUp**: Registrierung mit E-Mail und Passwort
- **signOut**: Abmeldung des Benutzers
- **resetPassword**: Initiierung des Passwort-Zurücksetzungsprozesses
- **updatePassword**: Aktualisierung des Benutzerpassworts

Und folgende Zustandsdaten:

- **session**: Die aktuelle Supabase-Sitzung
- **user**: Die Benutzerdaten des angemeldeten Benutzers
- **loading**: Ein Ladezustand für asynchrone Operationen

### Geschützte Routen

Die Anwendung verwendet Tanstack Router für das Routing. Geschützte Routen werden mit der `beforeLoad`-Funktion implementiert, die prüft, ob ein Benutzer angemeldet ist, bevor der Zugriff auf bestimmte Seiten gewährt wird:

```typescript
const erfassenRoute = new Route({
  getParentRoute: () => rootRoute,
  path: '/erfassen',
  component: ErfassenPage,
  beforeLoad: ({ context }) => {
    if (!context.session) {
      throw redirect({
        to: '/login',
        search: {
          redirect: '/erfassen',
        },
      });
    }
  },
});
```

Wenn kein Benutzer angemeldet ist, wird der Benutzer zur Login-Seite umgeleitet, wobei die ursprünglich angeforderte Seite als Weiterleitungsparameter übergeben wird.

Die Authentifizierungsdaten werden dem Router-Kontext in der `App.tsx`-Komponente hinzugefügt:

```typescript
function AppWithAuth() {
  const { user, session } = useAuth();

  useEffect(() => {
    router.update({
      context: {
        user,
        session,
      },
    });
  }, [user, session]);

  return <RouterProvider router={router} />;
}
```

## Authentifizierungsflüsse

### Registrierung

Der Registrierungsprozess wird in der `RegisterPage.tsx`-Komponente implementiert:

1. Der Benutzer gibt seine E-Mail-Adresse und ein Passwort ein
2. Die Eingaben werden validiert (Passwortlänge, Übereinstimmung der Passwörter)
3. Die `signUp`-Funktion aus dem Auth Context wird aufgerufen
4. Bei erfolgreicher Registrierung wird der Benutzer zur Login-Seite weitergeleitet

```typescript
const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault();
  
  // Validierung...

  try {
    setLoading(true);
    const { error } = await signUp(email, password);

    if (error) {
      throw error;
    }

    // Registrierung erfolgreich
    navigate({ to: '/login' });
  } catch (err) {
    console.error('Registration error:', err);
    setError('Registrierung fehlgeschlagen. Bitte versuchen Sie es erneut.');
  } finally {
    setLoading(false);
  }
};
```

### Anmeldung

Der Anmeldeprozess wird in der `LoginPage.tsx`-Komponente implementiert:

1. Der Benutzer gibt seine E-Mail-Adresse und sein Passwort ein
2. Die `signIn`-Funktion aus dem Auth Context wird aufgerufen
3. Bei erfolgreicher Anmeldung wird der Benutzer zur angeforderten Seite oder zur Startseite weitergeleitet

```typescript
const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault();

  // Validierung...

  try {
    setLoading(true);
    const { error } = await signIn(email, password);

    if (error) {
      throw error;
    }

    // Anmeldung erfolgreich, Weiterleitung zur angeforderten Seite oder Startseite
    navigate({ to: redirect });
  } catch (err) {
    console.error('Login error:', err);
    setError('Anmeldung fehlgeschlagen. Bitte überprüfen Sie Ihre Anmeldedaten.');
  } finally {
    setLoading(false);
  }
};
```

### Passwort zurücksetzen

Der Passwort-Zurücksetzungsprozess besteht aus zwei Teilen:

1. **Anforderung einer Zurücksetzungs-E-Mail** (`ForgotPasswordPage.tsx`):
   - Der Benutzer gibt seine E-Mail-Adresse ein
   - Die `resetPassword`-Funktion aus dem Auth Context wird aufgerufen
   - Eine E-Mail mit einem Zurücksetzungslink wird an den Benutzer gesendet

```typescript
const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault();
  
  // Validierung...

  try {
    setLoading(true);
    const { error } = await resetPassword(email);

    if (error) {
      throw error;
    }

    // Erfolg
    setMessage({ 
      type: 'success', 
      text: 'Eine E-Mail mit Anweisungen zum Zurücksetzen Ihres Passworts wurde gesendet. Bitte überprüfen Sie Ihren Posteingang.' 
    });
    setEmail(''); // Formular leeren
  } catch (err) {
    console.error('Password reset error:', err);
    setMessage({ 
      type: 'error', 
      text: 'Fehler beim Senden der Passwort-Zurücksetzungs-E-Mail. Bitte versuchen Sie es später erneut.' 
    });
  } finally {
    setLoading(false);
  }
};
```

2. **Festlegung eines neuen Passworts** (`ResetPasswordPage.tsx`):
   - Der Benutzer klickt auf den Link in der E-Mail und wird zur Seite für die Passwort-Zurücksetzung weitergeleitet
   - Supabase verarbeitet den Token in der URL und erstellt eine temporäre Sitzung
   - Der Benutzer gibt ein neues Passwort ein
   - Die `updatePassword`-Funktion aus dem Auth Context wird aufgerufen
   - Bei Erfolg wird der Benutzer zur Login-Seite weitergeleitet

```typescript
const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault();
  
  // Validierung...

  try {
    setLoading(true);
    const { error } = await updatePassword(password);

    if (error) {
      throw error;
    }

    // Erfolg
    setMessage({ 
      type: 'success', 
      text: 'Ihr Passwort wurde erfolgreich aktualisiert.' 
    });
    
    // Weiterleitung zur Login-Seite nach kurzer Verzögerung
    setTimeout(() => {
      navigate({ to: '/login' });
    }, 2000);
  } catch (err) {
    console.error('Password update error:', err);
    setMessage({ 
      type: 'error', 
      text: 'Fehler beim Aktualisieren des Passworts. Bitte versuchen Sie es später erneut.' 
    });
  } finally {
    setLoading(false);
  }
};
```

### Abmeldung

Die Abmeldung erfolgt durch Aufruf der `signOut`-Funktion aus dem Auth Context. Diese Funktion wird typischerweise in einer Navigationskomponente oder einem Benutzermenü aufgerufen.

```typescript
const handleSignOut = async () => {
  await signOut();
  // Weiterleitung zur Startseite oder Login-Seite
};
```

## Sicherheitsaspekte

Die Authentifizierungsimplementierung berücksichtigt mehrere Sicherheitsaspekte:

1. **PKCE-Fluss**: Die Anwendung verwendet den PKCE-Fluss (Proof Key for Code Exchange) für erhöhte Sicherheit bei der Authentifizierung.

2. **Passwortvalidierung**: Bei der Registrierung und Passwort-Zurücksetzung werden Passwörter auf Mindestlänge und Übereinstimmung geprüft.

3. **Geschützte Routen**: Sensible Bereiche der Anwendung sind durch Authentifizierungsprüfungen geschützt.

4. **Sichere Passwort-Zurücksetzung**: Der Passwort-Zurücksetzungsprozess verwendet sichere Links mit einmaligen Tokens.

5. **Automatische Token-Aktualisierung**: Tokens werden automatisch aktualisiert, wenn sie ablaufen, um eine unterbrechungsfreie Benutzererfahrung zu gewährleisten.

## Benutzerrollen und Berechtigungen

Die aktuelle Implementierung unterscheidet nicht zwischen verschiedenen Benutzerrollen (z.B. Administratoren und reguläre Benutzer). Alle angemeldeten Benutzer haben Zugriff auf die gleichen geschützten Routen.

Für zukünftige Entwicklungen könnte die Implementierung von Benutzerrollen und detaillierteren Berechtigungen in Betracht gezogen werden, um beispielsweise den Zugriff auf das Admin-Dashboard auf Administratoren zu beschränken.

Mögliche Implementierungsansätze:

1. **Supabase Row Level Security (RLS)**: Definieren von Sicherheitsrichtlinien auf Datenbankebene, die den Zugriff basierend auf Benutzerrollen einschränken.

2. **Benutzerdefinierte Ansprüche (Claims)**: Speichern von Rolleninformationen in benutzerdefinierten Ansprüchen im JWT-Token.

3. **Separate Benutzerrollentabelle**: Erstellen einer separaten Tabelle zur Speicherung von Benutzerrollen und Abfrage dieser Informationen nach der Anmeldung.