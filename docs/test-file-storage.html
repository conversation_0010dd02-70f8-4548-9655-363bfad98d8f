<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Storage Refactoring Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        code {
            background: #e9ecef;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>File Storage Refactoring Test Plan</h1>
    
    <div class="test-section info">
        <h2>🎯 Refactoring Objectives</h2>
        <ul>
            <li>Consolidate separate buckets into single <code>certificateuploads</code> bucket</li>
            <li>Implement standardized path structure: <code>userId/certificateId/fieldName_filename</code></li>
            <li>Update all file upload and display components</li>
            <li>Ensure Row Level Security (RLS) works correctly</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>📋 Test Checklist</h2>
        
        <h3>1. File Upload Tests</h3>
        <ul>
            <li>✅ Test building image upload in ObjektdatenPage</li>
            <li>✅ Test consumption bill uploads in VerbrauchPage</li>
            <li>✅ Verify files are stored with correct path structure</li>
            <li>✅ Check upload progress indicators work</li>
        </ul>

        <h3>2. File Display Tests</h3>
        <ul>
            <li>✅ Test file display in ZusammenfassungPage</li>
            <li>✅ Verify signed URLs are generated correctly</li>
            <li>✅ Check FileWithSignedUrl component works without bucket parameter</li>
            <li>✅ Test both image and PDF file display</li>
        </ul>

        <h3>3. Security Tests</h3>
        <ul>
            <li>⏳ Verify users can only access their own files</li>
            <li>⏳ Test RLS policy: <code>((bucket_id = 'certificateuploads'::text) AND ((storage.foldername(name))[1] = (auth.uid())::text))</code></li>
            <li>⏳ Ensure cross-user file access is blocked</li>
        </ul>

        <h3>4. Legacy Compatibility Tests</h3>
        <ul>
            <li>✅ Test getSignedUrl utility handles legacy bucket URLs</li>
            <li>✅ Verify existing file references still work</li>
        </ul>
    </div>

    <div class="test-section success">
        <h2>✅ Completed Changes</h2>
        <ul>
            <li><strong>fileUtils.ts</strong>: Updated to use consolidated bucket and handle legacy URLs</li>
            <li><strong>FileWithSignedUrl.tsx</strong>: Removed bucket parameter, uses utility function</li>
            <li><strong>ObjektdatenPage.tsx</strong>: Updated to use new bucket and path structure</li>
            <li><strong>VerbrauchPage.tsx</strong>: Updated to use new bucket and path structure</li>
            <li><strong>ZusammenfassungPage.tsx</strong>: Removed bucket parameters from FileWithSignedUrl calls</li>
        </ul>
    </div>

    <div class="test-section info">
        <h2>🔧 Manual Testing Steps</h2>
        
        <h3>Step 1: Test File Upload</h3>
        <ol>
            <li>Login to the application</li>
            <li>Create or select a certificate</li>
            <li>Navigate to Objektdaten page</li>
            <li>Upload a building image</li>
            <li>Verify file path in database follows pattern: <code>userId/certificateId/gebaeudebild_filename</code></li>
        </ol>

        <h3>Step 2: Test File Display</h3>
        <ol>
            <li>Navigate to Zusammenfassung page</li>
            <li>Verify uploaded image displays correctly</li>
            <li>Check that signed URL is generated properly</li>
            <li>Test clicking on file links opens files correctly</li>
        </ol>

        <h3>Step 3: Test Consumption Bills</h3>
        <ol>
            <li>Navigate to Verbrauch page</li>
            <li>Upload consumption bills (PDF and images)</li>
            <li>Verify multiple file uploads work</li>
            <li>Check file display in summary page</li>
        </ol>

        <h3>Step 4: Test Security</h3>
        <ol>
            <li>Create files with one user account</li>
            <li>Login with different user account</li>
            <li>Attempt to access first user's files directly</li>
            <li>Verify access is denied by RLS policy</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>🚀 Next Steps</h2>
        <ol>
            <li>Deploy the refactored code to development environment</li>
            <li>Run manual tests according to the checklist above</li>
            <li>Verify RLS policy is correctly applied in Supabase</li>
            <li>Test with multiple users to ensure security</li>
            <li>Monitor for any issues with existing files</li>
        </ol>
    </div>

    <div class="test-section error">
        <h2>⚠️ Important Notes</h2>
        <ul>
            <li><strong>No backwards compatibility:</strong> Old bucket references have been removed</li>
            <li><strong>RLS Policy Required:</strong> Must be applied manually in Supabase Console</li>
            <li><strong>Existing Files:</strong> Legacy files may need migration or the utility function handles them</li>
            <li><strong>Path Structure:</strong> New uploads use userId/certificateId/fieldName_filename pattern</li>
        </ul>
    </div>
</body>
</html>
