
**Phase 1: Grundgerüst und Routing**

* **Prompt 1.1: Projekt-Setup**
    * "Bitte erstelle ein neues React-Projekt mit Vite, konfiguriert für TypeScript und Tailwind CSS. Installiere und konfiguriere Tanstack Router für das grundlegende Routing."
* **Prompt 1.2: Basis-Layout und erste Routen**
    * "Erstelle ein Basis-Layout (z.B. mit Header und Footer) und richte folgende initiale Routen mit Tanstack Router ein:
        * `/`: Startseite (vorerst leer)
        * `/erfassen/objektdaten`: Start der Energieausweiserfassung (erste Formularseite)
        * `/zusammenfassung`: Zusammenfassungsseite (vorerst leer)
        * `/login`: Login-Seite für Benutzer (vorerst leer)
        * `/admin`: Admin-Dashboard (vorerst leer und geschützt)"

**Phase 2: Authentifizierung mit Supabase**

* **Prompt 2.1: Supabase Client Setup**
    * "Integriere den Supabase JavaScript-Client in das Projekt. Konfiguriere die Umgebungsvariablen für die Supabase URL und den Anon Key."
* **Prompt 2.2: Login-Seite und Authentifizierungslogik**
    * "Entwickle die Login-Seite (`/login`). Implementiere die Benutzerauthentifizierung (E-Mail/Passwort) mit Supabase Auth. Nach erfolgreichem Login soll der Nutzer auf eine definierte Seite weitergeleitet werden (z.B. `/erfassen` oder `/admin`). Implementiere auch eine Logout-Funktion."
* **Prompt 2.3: Registrierungsseite (Optional)**
    * "Erstelle eine Registrierungsseite, auf der sich neue Nutzer mit E-Mail und Passwort bei Supabase Auth registrieren können."
* **Prompt 2.4: Geschützte Routen**
    * "Implementiere Logik, um bestimmte Routen (z.B. `/erfassen/objektdaten`, `/zusammenfassung`, `/admin`) nur für authentifizierte Benutzer zugänglich zu machen. Nicht authentifizierte Benutzer sollen auf die Login-Seite umgeleitet werden."

**Phase 3: Formularseiten zur Dateneingabe (basierend auf der Schnittstellenbeschreibung)**

* **Hinweis für alle Formular-Prompts:** "Verwende Tanstack Forms für die Formularerstellung und -validierung. Die eingegebenen Daten sollen zwischengespeichert werden (z.B. mit Tanstack Query oder einem lokalen State Management), um einen Datenverlust bei Seitenwechsel zu vermeiden. Beziehe dich für die Feldnamen, möglichen Auswahlwerte und Validierungen auf die bereitgestellte `Schnittstellenbeschreibung-Import-Energieausweise.pdf`." ([1], [2], [3], [4], [5], [6], [7], [8])

* **Prompt 3.1: Formularseite 1 - Allgemeine Objektdaten & Kundendaten**
    * "Erstelle die erste Seite des Formulars unter der Route `/erfassen/objektdaten`. Diese Seite soll Felder für die allgemeinen Objektdaten (ID, Straße, Hausnr, PLZ, Ort) und Kundendaten (Anrede, Vorname, Nachname, Straße, Hausnr, PLZ, Ort, E-Mail, Telefon) gemäß der Schnittstellenbeschreibung enthalten." ([3])
* **Prompt 3.2: Formularseite 2 - Gebäudedetails Teil 1**
    * "Erstelle die zweite Formularseite unter `/erfassen/gebaeudedetails1`. Diese Seite soll Felder abdecken wie: DIBt-Registriernummer, Gebäudeteil Auto, BedarfVerbrauch, Anlass, Datenerhebung, nichtWohnGeb, isGebaeudehuelle, Nutzung1\_ID, Nutzung1 Flaeche, Baujahr, Modernisierung, Wohnfläche, Raumhöhe, Volumen, Wohneinheiten, Geschosse, Anbausituation, Keller beheizt, Klimatisiert, ergänzende Erläuterungen, baujahrHzErz." ([3], [4])
* **Prompt 3.3: Formularseite 3 - Gebäudedetails Teil 2 (Hülle, Dämmung)**
    * "Erstelle die dritte Formularseite unter `/erfassen/gebaeudedetails2`. Diese soll Felder für kuehlWfl, Originaldämmstandard, bjFensterAustausch, Fensterlüftung, Schachtlüftung, L Mit WRG, L Ohne WRG sowie die Erfassung von Bauteilen (Boden1, Dach1, Wand1 etc.) mit Bezeichnung, Material (massiv), ggf. Übergang, Fläche, U-Wert und Dämmung ermöglichen. Erlaube die dynamische Hinzufügung mehrerer Bauteile (z.B. mehrere Wände, Dächer)." ([4], [5])
* **Prompt 3.4: Formularseite 4 - Fenster**
    * "Erstelle die vierte Formularseite unter `/erfassen/fenster`. Diese Seite soll die Erfassung von Fenstern (Fenster1 etc.) mit Bezeichnung, Art, Fläche, U-Wert, Ausrichtung und Baujahr ermöglichen. Erlaube die dynamische Hinzufügung mehrerer Fenster." ([5])
* **Prompt 3.5: Formularseite 5 - Heizungssystem**
    * "Erstelle die fünfte Formularseite unter `/erfassen/heizung`. Diese Seite soll Felder für das Heizungssystem abdecken: Baujahr Hzg, Hzg\_Speicher\_Baujahr, Hzg\_Verteilung\_Baujahr, Hzg\_Übergabe, Hzg\_Verteilung\_Art, Hzg\_Kreistemperatur, Hzg\_Verteilung\_Dämmung, Hzg Speicher, Hzg Aufstellung, Hzg\_Technik, Hzg Energieträger, Hzg PrimFaktor." ([5], [6])
* **Prompt 3.6: Formularseite 6 - Trinkwarmwasser (TWW) & Lüftung**
    * "Erstelle die sechste Formularseite unter `/erfassen/tww-lueftung`. Diese Seite soll Felder für Trinkwarmwasser (TW\_Baujahr, TW\_Speicher\_Baujahr, TW\_Verteilung\_Baujahr, TW\_Verteilung Art, TW\_Verteilung\_Dämmung, TW\_Zirkulation, TW\_Speicher\_Standort, TW\_Technik, TW\_Solar, HZ\_Solar, TW\_WP, HZ WP) und Lüftung (Luft\_Baujahr, Luft\_Verteilung\_Baujahr, Luft Lage, Luft Typ) enthalten." ([6], [7])
* **Prompt 3.7: Formularseite 7 - Energieverbrauch (Verbrauchsrechnungen)**
    * "Erstelle die siebte Formularseite unter `/erfassen/verbrauch`. Diese Seite soll die Erfassung der Energieträger und Verbrauchsdaten für bis zu drei Jahre ermöglichen (ETr1 Kategorie, Heizung, TWW, ZusatzHz, Lueften, Licht, Kuehlen, Sonst, PrimFaktor, Anteil\_erneuerbar, Anteil KWK, isFw, gebaeudeNahErzeugt, Name, Jahr1\_von, Jahr1\_bis, Jahr1 Menge, Jahr1\_Menge\_TWW, Jahr1\_Leerstand etc. für ETr1, ETr2, ETr3). Ermögliche hier auch den Upload der letzten drei Verbrauchsrechnungen als Bild- oder PDF-Dateien." ([7])
* **Prompt 3.8: Formularseite 8 - Weitere Angaben & Bilderupload**
    * "Erstelle die achte Formularseite unter `/erfassen/weitere-angaben`. Diese Seite soll Felder für WSchVo77\_erfuellt ([7]) und den Upload von Gebäudebildern ermöglichen."
* **Prompt 3.9: Navigation zwischen Formularseiten**
    * "Implementiere eine klare Navigation (z.B. 'Weiter' und 'Zurück' Buttons) zwischen den einzelnen Formularseiten. Stelle sicher, dass der Fortschritt gespeichert wird."

**Phase 4: Zusammenfassungsseite**

* **Prompt 4.1: Tabellarische Zusammenfassung**
    * "Entwickle die Zusammenfassungsseite (`/zusammenfassung`). Zeige alle vom Benutzer in den Formularen eingegebenen Daten in einer übersichtlichen, tabellarischen Form an. Die Daten sollen aus der Supabase-Datenbank (Tanstack Query ) geladen werden."

**Phase 7: Supabase Integration (Storage & Datenpersistenz)**

* **Prompt 7.1: Speichern von Formulardaten in Supabase (nach Zahlung)**
    * "Implementiere die Logik, um die vollständigen Formulardaten des Energieausweises nach erfolgreicher Zahlung (Webhook von Stripe wäre ideal, für den Anfang kann dies simuliert oder manuell ausgelöst werden) in den entsprechenden Tabellen der Supabase-Datenbank zu speichern."
* **Prompt 7.2: Upload von Bildern und Verbrauchsrechnungen zu Supabase Storage**
    * "Implementiere die Funktionalität, um die hochgeladenen Gebäudebilder und Verbrauchsrechnungen in Supabase Storage zu speichern. Verknüpfe die Storage-Pfade mit dem entsprechenden Energieausweis-Datensatz in der Datenbank."

**Phase 5: Stripe Checkout Integration**

* **Prompt 5.1: Kauf-Button und Weiterleitung zu Stripe**
    * "Füge auf der Zusammenfassungsseite einen 'Kaufen'-Button hinzu. Bei Klick auf diesen Button soll der Nutzer zu Stripe Checkout weitergeleitet werden, um den Energieausweis zu bezahlen. (Hinweis: Die serverseitige Erstellung der Checkout-Session und die Webhook-Verarbeitung für den Zahlungsstatus werden in einem späteren Schritt oder durch ein separates Backend gehandhabt. Konzentriere dich hier auf die Frontend-Weiterleitung)."

**Phase 6: Admin-Dashboard**

* **Prompt 6.1: Admin-Dashboard-Seite**
    * "Entwickle die Basisstruktur der Admin-Dashboard-Seite (`/admin`). Diese Seite soll nur für eingeloggte Administratoren zugänglich sein (ggf. eine Rollenprüfung in Supabase implementieren)."
* **Prompt 6.2: Datenanzeige im Admin-Dashboard**
    * "Zeige im Admin-Dashboard eine Übersicht der erstellten Energieausweis-Datensätze an (z.B. eine Liste mit ID, Objektadresse, Kundennamen und Erstellungsdatum). Die Daten sollen aus der Supabase-Datenbank geladen werden (Annahme: Die Daten werden nach erfolgreicher Zahlung dort gespeichert)."
* **Prompt 6.3: CSV-Generierung und Download**
    * "Implementiere im Admin-Dashboard eine Funktion, um die ausgewählten oder alle Energieausweis-Datensätze als CSV-Datei zu exportieren. Die CSV-Datei soll dem Format der bereitgestellten Schnittstellenbeschreibung entsprechen (UTF-8 ohne BOM, Semikolon als Trennzeichen)." ([1])



Migration Plan
Identify Common and Unique Fields:

Review the Schnittstellenbeschreibung.md to identify fields common to all certificate types and those unique to each type.
Create Reusable Components:

Develop generic form components that can be reused across different certificate types.
Implement specific components for fields unique to each certificate type.
Refactor Existing Forms:

Refactor the existing forms to use the new reusable components.
Ensure that each certificate type has its own form structure, utilizing shared components where possible.
Implement Certificate Type Selection:

Add a mechanism for users to select the certificate type they are applying for.
Dynamically render the appropriate form based on the selected certificate type.
Test and Validate:

After each implementation step, test the forms to ensure they function correctly and validate as expected.
Implementation Steps
Step 1: Identify Common and Unique Fields
Prompt: "Review the Schnittstellenbeschreibung.md document and list all fields that are common across all certificate types and those that are unique to each type. This will help in designing reusable components."

Step 2: Create Reusable Form Components
Prompt: "Develop a set of reusable form components for common fields identified in Step 1. Ensure these components handle input, validation, and error display. Use the existing FormField component as a reference."

Step 3: Implement Specific Components for Unique Fields
Prompt: "For each certificate type, create specific form components for fields unique to that type. Ensure these components integrate seamlessly with the reusable components developed in Step 2."

Step 4: Refactor Existing Forms
Prompt: "Refactor the existing forms to utilize the new reusable and specific components. Ensure that the form structure is clear and maintainable, with a focus on code reuse."

Step 5: Implement Certificate Type Selection
Prompt: "Add a user interface element that allows users to select the type of energy certificate they are applying for. Based on the selection, dynamically render the appropriate form using the components developed in previous steps."

Step 6: Test and Validate Each Form
Prompt: "Test each form for the different certificate types to ensure they function correctly. Validate that all fields are captured accurately and that the form submission process works as expected. Provide feedback for any issues encountered."

By following this plan, you can systematically separate the certificate types while maximizing code reuse and ensuring a maintainable codebase. Each step allows for testing and feedback, ensuring that the implementation meets the project requirements.