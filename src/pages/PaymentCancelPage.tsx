import { Link, useSearch, useNavigate } from '@tanstack/react-router';
import { useEffect } from 'react';
import { ErrorMessage } from '../components/ui/StatusMessages';
import { supabase } from '../lib/supabase';

interface PaymentCancelSearchParams {
  error?: string;
}

export const PaymentCancelPage = () => {
  const search = useSearch({ from: '/payment-cancel' }) as PaymentCancelSearchParams;
  const navigate = useNavigate();
  const { error } = search;

  // Function to restore form state when returning from canceled payment
  const restoreFormState = () => {
    const savedState = localStorage.getItem('checkoutFormState');
    if (savedState) {
      try {
        const parsedState = JSON.parse(savedState);
        // Check if the saved state is still valid (e.g., not too old)
        const savedTime = new Date(parsedState.timestamp).getTime();
        const currentTime = new Date().getTime();
        const hourInMs = 60 * 60 * 1000;
        
        // If saved state is less than 1 hour old, it's still valid
        if (currentTime - savedTime < hourInMs) {
          console.log('Restored form state from canceled payment');
        }
      } catch (e) {
        console.error('Error parsing saved form state:', e);
      }
    }
  };

  // Function for programmatic navigation
  const goToZusammenfassung = () => {
    navigate({ to: '/erfassen/zusammenfassung' });
  };

  const goToMyCertificates = () => {
    navigate({ to: '/meine-zertifikate' });
  };

  // Track cancellation analytics
  const trackCancellation = async () => {
    const sessionId = localStorage.getItem('lastCheckoutSessionId');
    if (sessionId) {
      try {
        // Log cancellation event for analytics
        await supabase.functions.invoke('log-payment-cancellation', {
          body: {
            sessionId,
            reason: error || 'user_cancelled',
            userAgent: navigator.userAgent,
            timestamp: new Date().toISOString()
          }
        });
        console.log('Payment cancellation logged successfully');
      } catch (logError) {
        console.error('Error logging payment cancellation:', logError);
        // Don't fail the page if logging fails
      }
    }
  };

  // Restore form state when component mounts
  useEffect(() => {
    restoreFormState();

    // Track the cancellation
    trackCancellation();

    // Clean up checkout session ID as it's no longer needed
    localStorage.removeItem('lastCheckoutSessionId');
  }, [error]);

  const getErrorMessage = (errorCode?: string) => {
    switch (errorCode) {
      case 'payment_cancelled':
        return 'Sie haben die Zahlung abgebrochen.';
      case 'payment_failed':
        return 'Die Zahlung konnte nicht verarbeitet werden. Bitte überprüfen Sie Ihre Zahlungsinformationen.';
      case 'session_expired':
        return 'Die Zahlungssitzung ist abgelaufen. Bitte versuchen Sie es erneut.';
      case 'card_declined':
        return 'Ihre Karte wurde abgelehnt. Bitte verwenden Sie eine andere Zahlungsmethode.';
      case 'insufficient_funds':
        return 'Unzureichende Deckung. Bitte überprüfen Sie Ihr Konto oder verwenden Sie eine andere Karte.';
      case 'authentication_required':
        return 'Zusätzliche Authentifizierung erforderlich. Bitte versuchen Sie es erneut.';
      case 'processing_error':
        return 'Ein technischer Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.';
      default:
        return 'Die Zahlung wurde nicht abgeschlossen.';
    }
  };

  const getDetailedGuidance = (errorCode?: string) => {
    switch (errorCode) {
      case 'card_declined':
        return {
          title: 'Karte abgelehnt',
          suggestions: [
            'Überprüfen Sie, ob Ihre Karte für Online-Zahlungen freigeschaltet ist',
            'Kontaktieren Sie Ihre Bank, um sicherzustellen, dass keine Sperre vorliegt',
            'Versuchen Sie es mit einer anderen Karte',
            'Überprüfen Sie die eingegebenen Kartendaten auf Richtigkeit'
          ]
        };
      case 'insufficient_funds':
        return {
          title: 'Unzureichende Deckung',
          suggestions: [
            'Überprüfen Sie Ihren Kontostand',
            'Verwenden Sie eine andere Karte mit ausreichender Deckung',
            'Kontaktieren Sie Ihre Bank für weitere Informationen'
          ]
        };
      case 'session_expired':
        return {
          title: 'Sitzung abgelaufen',
          suggestions: [
            'Ihre Zahlungssitzung war zu lange inaktiv',
            'Kehren Sie zur Zusammenfassung zurück und starten Sie den Zahlvorgang erneut',
            'Ihre eingegebenen Daten sind weiterhin gespeichert'
          ]
        };
      case 'processing_error':
        return {
          title: 'Technischer Fehler',
          suggestions: [
            'Ein vorübergehender technischer Fehler ist aufgetreten',
            'Warten Sie einen Moment und versuchen Sie es erneut',
            'Falls das Problem weiterhin besteht, kontaktieren Sie unseren Support'
          ]
        };
      default:
        return {
          title: 'Allgemeine Problembehebung',
          suggestions: [
            'Überprüfen Sie Ihre Internetverbindung',
            'Stellen Sie sicher, dass JavaScript in Ihrem Browser aktiviert ist',
            'Versuchen Sie es mit einem anderen Browser',
            'Deaktivieren Sie vorübergehend Ihren Adblocker'
          ]
        };
    }
  };

  const guidance = getDetailedGuidance(error);

  return (
    <div className="max-w-2xl mx-auto px-4 py-8">
      <ErrorMessage message={getErrorMessage(error)} type="warning" />

      <div className="bg-white shadow-md rounded-lg p-6 mt-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Zahlung abgebrochen</h1>

        <div className="space-y-4">
          <p className="text-gray-600">
            Ihre Zahlung wurde nicht abgeschlossen. Keine Sorge - es wurden keine Gebühren erhoben.
          </p>

          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Was können Sie jetzt tun?</h3>
            <ul className="list-disc list-inside space-y-2 text-gray-600">
              <li>Kehren Sie zur Zusammenfassung zurück und versuchen Sie die Zahlung erneut</li>
              <li>Überprüfen Sie Ihre Zahlungsinformationen</li>
              <li>Kontaktieren Sie uns bei anhaltenden Problemen</li>
            </ul>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-md font-medium text-gray-900 mb-2">{guidance.title}:</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              {guidance.suggestions.map((suggestion, index) => (
                <li key={index}>• {suggestion}</li>
              ))}
            </ul>
          </div>
        </div>

        <div className="mt-8 pt-6 border-t border-gray-200">
          <div className="flex flex-col sm:flex-row gap-4">
            <button
              onClick={goToZusammenfassung}
              className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
            >
              Zahlung erneut versuchen
            </button>
            <button
              onClick={goToMyCertificates}
              className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              Zu meinen Zertifikaten
            </button>
            <Link
              to="/"
              className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              Zur Startseite
            </Link>
          </div>
        </div>

        <div className="mt-6 p-4 bg-green-50 rounded-lg">
          <h4 className="text-sm font-medium text-green-900 mb-2">Benötigen Sie Hilfe?</h4>
          <p className="text-sm text-green-700">
            Falls Sie weiterhin Probleme haben, kontaktieren Sie uns gerne:
          </p>
          <div className="mt-2 text-sm text-green-700">
            <p>📧 E-Mail: <EMAIL></p>
            <p>📞 Telefon: +49 (0) 123 456 789</p>
            <p>🕒 Montag - Freitag: 9:00 - 17:00 Uhr</p>
          </div>
        </div>
      </div>
    </div>
  );
};
