<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSV Export Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-left: 4px solid #28a745; }
        .error { background-color: #f8d7da; border-left: 4px solid #dc3545; }
        .info { background-color: #d1ecf1; border-left: 4px solid #17a2b8; }
        .warning { background-color: #fff3cd; border-left: 4px solid #ffc107; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .comparison-table th, .comparison-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .comparison-table th {
            background-color: #f2f2f2;
        }
        .match { background-color: #d4edda; }
        .mismatch { background-color: #f8d7da; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 CSV Export Test</h1>
        <p>This page tests the CSV export functionality to ensure it matches the reference file structure.</p>
        
        <div class="test-section info">
            <h2>📋 Test Instructions</h2>
            <ol>
                <li>Click "Run CSV Export Test" to test the basic functionality</li>
                <li>Click "Compare with Reference" to check against the expected structure</li>
                <li>Review the results below</li>
            </ol>
            <button onclick="runBasicTest()">Run CSV Export Test</button>
            <button onclick="compareWithReference()">Compare with Reference</button>
            <button onclick="downloadTestCsv()">Download Test CSV</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        // Sample test data
        const sampleData = {
            id: 'test-certificate-id',
            created_at: '2025-07-03T10:00:00Z',
            updated_at: '2025-07-03T10:00:00Z',
            user_id: 'test-user-id',
            certificate_type: 'WG/V',
            order_number: 'EA-370B0B8C',
            payment_status: 'paid',
            status: 'completed',
            stripe_checkout_session_id: null,
            
            objektdaten: {
                PLZ: '99876',
                Ort: 'Weimar',
                Strasse: 'Muster',
                Hausnr: '1',
                gebaeudeteil: 'GT_GANZES_GEB'
            },
            
            gebaeudedetails1: {
                Anlass: 'AG_VERMIETUNG',
                isGebaeudehuelle: '1',
                Baujahr: '1975',
                Wohneinheiten: '2',
                Wohnfläche: '123',
                Modernisierung: '2000',
                BedarfVerbrauch: 'V',
                Datenerhebung: '2',
                nichtWohnGeb: '0'
            },
            
            gebaeudedetails2: {
                Keller_beheizt: '1',
                Klimatisiert: '1',
                kuehlWfl: '120',
                Hzg_Baujahr: '1995',
                Fensterlüftung: '0',
                Schachtlüftung: '0',
                L_Mit_WRG: '0',
                L_Ohne_WRG: '0',
                bjFensterAustausch: '2000',
                Boden1_Dämmung: '0',
                Dach1_Dämmung: '18',
                Wand1_Dämmung: '15'
            },
            
            verbrauchsdaten: {
                ETr1_Kategorie: 'BK_UMWELT',
                ETr1_Heizung: '1',
                ETr1_TWW: '1',
                ETr1_Anteil_erneuerbar: '0',
                ETr1_Anteil_KWK: '0',
                ETr1_isFw: '0',
                ETr1_gebaeudeNahErzeugt: '0',
                ETr1_Jahr1_von: '24.05.2025',
                ETr1_Jahr1_bis: '27.10.2025',
                ETr1_Jahr1_Menge: '15000',
                ETr1_Jahr1_Menge_TWW: '113.46',
                ETr1_Jahr1_Leerstand: '0',
                ETr1_Jahr2_von: '24.05.2024',
                ETr1_Jahr2_bis: '24.05.2025',
                ETr1_Jahr2_Menge: '13000',
                ETr1_Jahr2_Menge_TWW: '',
                ETr1_Jahr2_Leerstand: '0',
                ETr1_Jahr3_von: '24.05.2023',
                ETr1_Jahr3_bis: '24.05.2024',
                ETr1_Jahr3_Menge: '14000',
                ETr1_Jahr3_Menge_TWW: '',
                ETr1_Jahr3_Leerstand: '0',
                ETr1_PrimFaktor: '1',
                ETr2_Heizung: '1',
                ETr2_PrimFaktor: '1',
                HZ_Solar: '0',
                TW_WP: '0',
                HZ_WP: '0'
            },
            
            trinkwarmwasser: {
                TW_Solar: '0'
            },
            
            fenster: null,
            heizung: null,
            lueftung: null
        };

        // Reference headers from EA_Verbrauch_WG.csv
        const referenceHeaders = [
            'BOM', 'ID', 'Anlass', 'PLZ', 'Ort', 'Straße', 'Hausnr', 'isGebaeudehuelle', 'Baujahr', 
            'Wohneinheiten', 'Wohnfläche', 'gebaeudeteil', 'Keller_beheizt', 'bilderStreams_0', 
            'bilderStreams_1', 'bilderStreams_2', 'Klimatisiert', 'kuehlWfl', 'passiveKuehlung', 
            'fernKuehlung', 'stromKuehlung', 'waermeKuehlung', 'kaiAnzahl', 'kaiDatum', 'baujahrHzErz', 
            'TW_Solar', 'HZ_Solar', 'TW_WP', 'HZ_WP', 'Fensterlüftung', 'Schachtlüftung', 'L_Mit_WRG', 
            'L_Ohne_WRG', 'Modernisierung', 'Dach1_Dämmung', 'Wand1_Dämmung', 'Boden1_Dämmung', 
            'bjFensterAustausch', 'ETr1_Kategorie', 'ETr1_Anteil_erneuerbar', 'ETr1_Anteil_KWK', 
            'ETr1_isFw', 'ETr1_gebaeudeNahErzeugt', 'ETr1_Jahr1_von', 'ETr1_Jahr1_bis', 'ETr1_Jahr1_Menge', 
            'ETr1_TWW', 'ETr1_Jahr1_Menge_TWW', 'ETr1_Jahr1_Leerstand', 'ETr1_Jahr2_von', 'ETr1_Jahr2_bis', 
            'ETr1_Jahr2_Menge', 'ETr1_Jahr2_Menge_TWW', 'ETr1_Jahr2_Leerstand', 'ETr1_Jahr3_von', 
            'ETr1_Jahr3_bis', 'ETr1_Jahr3_Menge', 'ETr1_Jahr3_Menge_TWW', 'ETr1_Jahr3_Leerstand', 
            'ETr1_PrimFaktor', 'ETr2_Kategorie', 'ETr2_Jahr1_von', 'ETr2_Jahr1_bis', 'ETr2_Jahr1_Menge', 
            'ETr2_TWW', 'ETr2_Jahr1_Menge_TWW', 'ETr2_Jahr1_Leerstand', 'ETr2_Jahr2_von', 'ETr2_Jahr2_bis', 
            'ETr2_Jahr2_Menge', 'ETr2_Jahr2_Menge_TWW', 'ETr2_Jahr2_Leerstand', 'ETr2_Jahr3_von', 
            'ETr2_Jahr3_bis', 'ETr2_Jahr3_Menge', 'ETr2_Jahr3_Menge_TWW', 'ETr2_Jahr3_Leerstand', 
            'ETr1_Heizung', 'ETr2_Heizung', 'ETr2_PrimFaktor', 'Gebäudetyp', 'Datenerhebung', 
            'BedarfVerbrauch', 'ETr2_Anteil_KWK', 'ETr2_Anteil_erneuerbar', 'ETr2_isFw', 'ETr2_gebaeudeNahErzeugt'
        ];

        function addResult(content, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-section ${type}`;
            div.innerHTML = content;
            resultsDiv.appendChild(div);
        }

        function runBasicTest() {
            addResult('<h3>🧪 Running Basic CSV Export Test...</h3>', 'info');
            
            try {
                // This would normally import from the actual module
                // For this test, we'll simulate the CSV generation
                const mockCsv = generateMockCsv();
                
                addResult(`
                    <h3>✅ Basic Test Results</h3>
                    <p><strong>CSV Generated:</strong> Yes</p>
                    <p><strong>Headers:</strong> ${mockCsv.headers.length} columns</p>
                    <p><strong>Data Rows:</strong> 1</p>
                    <pre>${mockCsv.content}</pre>
                `, 'success');
                
            } catch (error) {
                addResult(`
                    <h3>❌ Basic Test Failed</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                `, 'error');
            }
        }

        function compareWithReference() {
            addResult('<h3>🔍 Comparing with Reference File...</h3>', 'info');
            
            const mockCsv = generateMockCsv();
            const generatedHeaders = mockCsv.headers;
            
            const missing = referenceHeaders.filter(h => !generatedHeaders.includes(h));
            const extra = generatedHeaders.filter(h => !referenceHeaders.includes(h));
            
            let comparisonHtml = `
                <h3>📊 Comparison Results</h3>
                <p><strong>Reference Headers:</strong> ${referenceHeaders.length}</p>
                <p><strong>Generated Headers:</strong> ${generatedHeaders.length}</p>
            `;
            
            if (missing.length === 0 && extra.length === 0) {
                comparisonHtml += '<p class="match">✅ Perfect match! All headers present and no extras.</p>';
            } else {
                if (missing.length > 0) {
                    comparisonHtml += `<p class="mismatch">❌ Missing headers: ${missing.join(', ')}</p>`;
                }
                if (extra.length > 0) {
                    comparisonHtml += `<p class="mismatch">❌ Extra headers: ${extra.join(', ')}</p>`;
                }
            }
            
            addResult(comparisonHtml, missing.length === 0 && extra.length === 0 ? 'success' : 'warning');
        }

        function downloadTestCsv() {
            const mockCsv = generateMockCsv();
            const blob = new Blob([mockCsv.content], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            
            link.setAttribute('href', url);
            link.setAttribute('download', 'test-export-WG_V-2025-07-03.csv');
            link.style.visibility = 'hidden';
            
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            URL.revokeObjectURL(url);
            
            addResult('<h3>📥 Test CSV Downloaded</h3><p>Check your downloads folder for the test CSV file.</p>', 'success');
        }

        function generateMockCsv() {
            // This simulates what our actual CSV export should generate
            const headers = referenceHeaders;
            const dataRow = headers.map(header => {
                // Generate appropriate test data for each field
                switch(header) {
                    case 'BOM': return '';
                    case 'ID': return 'EA-370B0B8C';
                    case 'Anlass': return 'AG_VERMIETUNG';
                    case 'PLZ': return '99876';
                    case 'Ort': return 'Weimar';
                    case 'Straße': return 'Muster';
                    case 'Hausnr': return '1';
                    case 'isGebaeudehuelle': return '1';
                    case 'Baujahr': return '1975';
                    case 'Wohneinheiten': return '2';
                    case 'Wohnfläche': return '123';
                    case 'gebaeudeteil': return 'GT_GANZES_GEB';
                    case 'Keller_beheizt': return '1';
                    case 'Klimatisiert': return '1';
                    case 'kuehlWfl': return '120';
                    case 'baujahrHzErz': return '1995';
                    case 'Modernisierung': return '2000';
                    case 'Dach1_Dämmung': return '18';
                    case 'Wand1_Dämmung': return '15';
                    case 'Boden1_Dämmung': return '0';
                    case 'bjFensterAustausch': return '2000';
                    case 'ETr1_Kategorie': return 'BK_UMWELT';
                    case 'ETr1_Jahr1_von': return '24.05.2025';
                    case 'ETr1_Jahr1_bis': return '27.10.2025';
                    case 'ETr1_Jahr1_Menge': return '15000';
                    case 'BedarfVerbrauch': return 'V';
                    default: return header.includes('_von') || header.includes('_bis') ? '' : 
                             header.includes('Heizung') || header.includes('TWW') ? '1' : 
                             header.includes('#NV') ? '#NV' : '0';
                }
            });
            
            const content = headers.join(';') + '\n' + dataRow.join(';');
            
            return {
                headers,
                content
            };
        }

        // Auto-run basic test on page load
        window.onload = function() {
            addResult('<h3>🚀 CSV Export Test Page Loaded</h3><p>Ready to test CSV export functionality.</p>', 'info');
        };
    </script>
</body>
</html>
