import { memo } from 'react';
import { useField } from '@tanstack/react-form';
import { formatValidationError } from '../../utils/formValidation';

interface EnhancedSelectFieldProps {
  name: string;
  label: string;
  options: { value: string; label: string }[];
  required?: boolean;
  form: any;
  helpText?: string;
  placeholder?: string;
}

/**
 * Enhanced SelectField component with improved error display and validation
 */
export const EnhancedSelectField = memo(({
  name,
  label,
  options,
  required = true,
  form,
  helpText,
  placeholder = 'Bitte wählen...'
}: EnhancedSelectFieldProps) => {
  const { state, handleChange, handleBlur } = useField({
    name,
    form,
  });

  // Check if field has errors
  const hasErrors = state.meta.errors.length > 0;
  const errors: (string | undefined)[] = state.meta.errors || [];

  // Format errors for better display
    const formattedErrors = errors
      .filter((error): error is string => typeof error === 'string')
      .map((error: string) => formatValidationError(error, String(name)));

  return (
    <div className="mb-4">
      <label htmlFor={name} className="block text-sm font-medium text-gray-700 mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      
      <div className="relative">
        <select
          id={name}
          name={name}
          value={state.value ? String(state.value) : ''}
          onChange={(e) => handleChange(e.target.value)}
          onBlur={handleBlur}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 transition-colors ${
            hasErrors 
              ? 'border-red-500 focus:ring-red-500 focus:border-red-500 bg-red-50' 
              : 'border-gray-300 focus:ring-green-500 focus:border-green-500'
          }`}
          aria-invalid={hasErrors}
          aria-describedby={hasErrors ? `${name}-error` : helpText ? `${name}-help` : undefined}
        >
          <option value="">{placeholder}</option>
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        
        {/* Error icon */}
        {hasErrors && (
          <div className="absolute inset-y-0 right-8 pr-3 flex items-center pointer-events-none">
            <svg className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
        )}
      </div>

      {/* Error messages */}
      {hasErrors && (
        <div id={`${name}-error`} className="mt-1">
          {formattedErrors.map((error, index) => (
            <p key={index} className="text-sm text-red-600 flex items-start">
              <svg className="h-4 w-4 text-red-500 mt-0.5 mr-1 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
              {error}
            </p>
          ))}
        </div>
      )}

      {/* Help text */}
      {!hasErrors && helpText && (
        <p id={`${name}-help`} className="mt-1 text-sm text-gray-500">
          {helpText}
        </p>
      )}
    </div>
  );
});

EnhancedSelectField.displayName = 'EnhancedSelectField';
