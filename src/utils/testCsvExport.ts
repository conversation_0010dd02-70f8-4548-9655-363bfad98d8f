import { generateCsvData, generateCsvFilename } from './csvExport';
import { type EnergieausweisData } from '../types/csv';

// Sample test data that matches the structure from the current export
const sampleWGVCertificateData: EnergieausweisData = {
  id: 'test-certificate-id',
  created_at: '2025-07-03T10:00:00Z',
  updated_at: '2025-07-03T10:00:00Z',
  user_id: 'test-user-id',
  certificate_type: 'WG/V',
  order_number: 'EA-370B0B8C',
  payment_status: 'paid',
  status: 'completed',
  stripe_checkout_session_id: null,
  
  objektdaten: {
    PLZ: '99876',
    Ort: 'Weimar',
    Strasse: 'Muster',
    Hausnr: '1',
    gebaeudeteil: 'GT_GANZES_GEB'
  },
  
  gebaeudedetails1: {
    Anlass: 'AG_VERMIETUNG',
    isGebaeudehuelle: '1',
    <PERSON>ujahr: '1975',
    Wohneinheiten: '2',
    Wohnfl<PERSON><PERSON>: '123',
    Modernisierung: '2000',
    BedarfVerbrauch: 'V',
    Datenerhebung: '2',
    nichtWohnGeb: '0'
  },
  
  gebaeudedetails2: {
    Keller_beheizt: '1',
    Klimatisiert: '1',
    kuehlWfl: '120',
    Hzg_Baujahr: '1995',
    Fensterlüftung: '0',
    Schachtlüftung: '0',
    L_Mit_WRG: '0',
    L_Ohne_WRG: '0',
    bjFensterAustausch: '2000',
    // Standalone Dämmung fields for WG/V
    Boden1_Dämmung: '0',
    Dach1_Dämmung: '18',
    Wand1_Dämmung: '15'
  },
  
  verbrauchsdaten: {
    ETr1_Kategorie: 'BK_UMWELT',
    ETr1_Heizung: '1',
    ETr1_TWW: '1',
    ETr1_Anteil_erneuerbar: '0',
    ETr1_Anteil_KWK: '0',
    ETr1_isFw: '0',
    ETr1_gebaeudeNahErzeugt: '0',
    ETr1_Jahr1_von: '24.05.2025',
    ETr1_Jahr1_bis: '27.10.2025',
    ETr1_Jahr1_Menge: '15000',
    ETr1_Jahr1_Menge_TWW: '113.46',
    ETr1_Jahr1_Leerstand: '0',
    ETr1_Jahr2_von: '24.05.2024',
    ETr1_Jahr2_bis: '24.05.2025',
    ETr1_Jahr2_Menge: '13000',
    ETr1_Jahr2_Menge_TWW: '',
    ETr1_Jahr2_Leerstand: '0',
    ETr1_Jahr3_von: '24.05.2023',
    ETr1_Jahr3_bis: '24.05.2024',
    ETr1_Jahr3_Menge: '14000',
    ETr1_Jahr3_Menge_TWW: '',
    ETr1_Jahr3_Leerstand: '0',
    ETr1_PrimFaktor: '1',
    ETr2_Heizung: '1',
    ETr2_PrimFaktor: '1',
    HZ_Solar: '0',
    TW_WP: '0',
    HZ_WP: '0'
  },
  
  trinkwarmwasser: {
    TW_Solar: '0'
  },
  
  fenster: null,
  heizung: null,
  lueftung: null
};

/**
 * Test function to verify CSV export functionality
 */
export function testCsvExport(): void {
  console.log('🧪 Testing CSV Export Functionality');
  console.log('===================================');
  
  try {
    // Test CSV generation
    const csvContent = generateCsvData(sampleWGVCertificateData);
    console.log('✅ CSV generation successful');
    
    // Test filename generation
    const filename = generateCsvFilename(sampleWGVCertificateData);
    console.log(`✅ Filename generation successful: ${filename}`);
    
    // Split CSV into lines for analysis
    const lines = csvContent.split('\n');
    const headers = lines[0].split(';');
    const dataRow = lines[1].split(';');
    
    console.log(`📊 CSV Structure:`);
    console.log(`   - Headers: ${headers.length} columns`);
    console.log(`   - Data rows: ${lines.length - 1}`);
    console.log(`   - Data columns: ${dataRow.length}`);
    
    // Check if headers match expected structure
    const expectedFirstColumns = ['BOM', 'ID', 'Anlass', 'PLZ', 'Ort', 'Straße', 'Hausnr'];
    const actualFirstColumns = headers.slice(0, expectedFirstColumns.length);
    
    const headersMatch = expectedFirstColumns.every((col, index) => col === actualFirstColumns[index]);
    console.log(`✅ Header structure matches reference: ${headersMatch}`);
    
    if (!headersMatch) {
      console.log(`❌ Expected: ${expectedFirstColumns.join(';')}`);
      console.log(`❌ Actual:   ${actualFirstColumns.join(';')}`);
    }
    
    // Check for customer data fields (should NOT be present)
    const customerFields = ['Kunden_Anrede', 'Kunden_Vorname', 'Kunden_Nachname'];
    const hasCustomerFields = customerFields.some(field => headers.includes(field));
    console.log(`✅ Customer fields removed: ${!hasCustomerFields}`);
    
    if (hasCustomerFields) {
      const foundCustomerFields = customerFields.filter(field => headers.includes(field));
      console.log(`❌ Found customer fields: ${foundCustomerFields.join(', ')}`);
    }
    
    // Check for Dämmung fields (should be present)
    const daemmungFields = ['Dach1_Dämmung', 'Wand1_Dämmung', 'Boden1_Dämmung'];
    const hasDaemmungFields = daemmungFields.every(field => headers.includes(field));
    console.log(`✅ Dämmung fields present: ${hasDaemmungFields}`);
    
    if (!hasDaemmungFields) {
      const missingDaemmungFields = daemmungFields.filter(field => !headers.includes(field));
      console.log(`❌ Missing Dämmung fields: ${missingDaemmungFields.join(', ')}`);
    }
    
    // Output the generated CSV for manual inspection
    console.log('\n📄 Generated CSV Content:');
    console.log('Headers:', headers.join(';'));
    console.log('Data:   ', dataRow.join(';'));
    
    console.log('\n🎉 CSV Export Test Completed Successfully!');
    
  } catch (error) {
    console.error('❌ CSV Export Test Failed:', error);
    throw error;
  }
}

/**
 * Compare generated CSV with reference file structure
 */
export function compareWithReference(): void {
  console.log('\n🔍 Comparing with Reference File');
  console.log('=================================');
  
  // Reference file headers from EA_Verbrauch_WG.csv
  const referenceHeaders = [
    'BOM', 'ID', 'Anlass', 'PLZ', 'Ort', 'Straße', 'Hausnr', 'isGebaeudehuelle', 'Baujahr', 
    'Wohneinheiten', 'Wohnfläche', 'gebaeudeteil', 'Keller_beheizt', 'bilderStreams_0', 
    'bilderStreams_1', 'bilderStreams_2', 'Klimatisiert', 'kuehlWfl', 'passiveKuehlung', 
    'fernKuehlung', 'stromKuehlung', 'waermeKuehlung', 'kaiAnzahl', 'kaiDatum', 'baujahrHzErz', 
    'TW_Solar', 'HZ_Solar', 'TW_WP', 'HZ_WP', 'Fensterlüftung', 'Schachtlüftung', 'L_Mit_WRG', 
    'L_Ohne_WRG', 'Modernisierung', 'Dach1_Dämmung', 'Wand1_Dämmung', 'Boden1_Dämmung', 
    'bjFensterAustausch', 'ETr1_Kategorie', 'ETr1_Anteil_erneuerbar', 'ETr1_Anteil_KWK', 
    'ETr1_isFw', 'ETr1_gebaeudeNahErzeugt', 'ETr1_Jahr1_von', 'ETr1_Jahr1_bis', 'ETr1_Jahr1_Menge', 
    'ETr1_TWW', 'ETr1_Jahr1_Menge_TWW', 'ETr1_Jahr1_Leerstand', 'ETr1_Jahr2_von', 'ETr1_Jahr2_bis', 
    'ETr1_Jahr2_Menge', 'ETr1_Jahr2_Menge_TWW', 'ETr1_Jahr2_Leerstand', 'ETr1_Jahr3_von', 
    'ETr1_Jahr3_bis', 'ETr1_Jahr3_Menge', 'ETr1_Jahr3_Menge_TWW', 'ETr1_Jahr3_Leerstand', 
    'ETr1_PrimFaktor', 'ETr2_Kategorie', 'ETr2_Jahr1_von', 'ETr2_Jahr1_bis', 'ETr2_Jahr1_Menge', 
    'ETr2_TWW', 'ETr2_Jahr1_Menge_TWW', 'ETr2_Jahr1_Leerstand', 'ETr2_Jahr2_von', 'ETr2_Jahr2_bis', 
    'ETr2_Jahr2_Menge', 'ETr2_Jahr2_Menge_TWW', 'ETr2_Jahr2_Leerstand', 'ETr2_Jahr3_von', 
    'ETr2_Jahr3_bis', 'ETr2_Jahr3_Menge', 'ETr2_Jahr3_Menge_TWW', 'ETr2_Jahr3_Leerstand', 
    'ETr1_Heizung', 'ETr2_Heizung', 'ETr2_PrimFaktor', 'Gebäudetyp', 'Datenerhebung', 
    'BedarfVerbrauch', 'ETr2_Anteil_KWK', 'ETr2_Anteil_erneuerbar', 'ETr2_isFw', 'ETr2_gebaeudeNahErzeugt'
  ];
  
  // Generate CSV and extract headers
  const csvContent = generateCsvData(sampleWGVCertificateData);
  const generatedHeaders = csvContent.split('\n')[0].split(';');
  
  console.log(`Reference headers: ${referenceHeaders.length}`);
  console.log(`Generated headers: ${generatedHeaders.length}`);
  
  // Check for exact match
  const exactMatch = referenceHeaders.length === generatedHeaders.length && 
    referenceHeaders.every((header, index) => header === generatedHeaders[index]);
  
  console.log(`✅ Exact header match: ${exactMatch}`);
  
  if (!exactMatch) {
    // Find differences
    const missing = referenceHeaders.filter(header => !generatedHeaders.includes(header));
    const extra = generatedHeaders.filter(header => !referenceHeaders.includes(header));
    
    if (missing.length > 0) {
      console.log(`❌ Missing headers: ${missing.join(', ')}`);
    }
    
    if (extra.length > 0) {
      console.log(`❌ Extra headers: ${extra.join(', ')}`);
    }
    
    // Check order differences
    for (let i = 0; i < Math.min(referenceHeaders.length, generatedHeaders.length); i++) {
      if (referenceHeaders[i] !== generatedHeaders[i]) {
        console.log(`❌ Order difference at position ${i}: expected '${referenceHeaders[i]}', got '${generatedHeaders[i]}'`);
        break;
      }
    }
  }
}
