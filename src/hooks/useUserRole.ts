import { useQuery } from '@tanstack/react-query';
import { supabase } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext'; // Assuming useAuth provides the current user session

export function useUserRole() {
  const { session } = useAuth(); // Get the current session from AuthContext

  return useQuery({
    queryKey: ['userRole', session?.user?.id],
    queryFn: async () => {
      if (!session?.user) {
        return null; // No authenticated user, no role
      }

      const { data, error } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', session.user.id)
        .single();

      if (error) {
        console.error('Error fetching user role:', error);
        throw new Error('Could not fetch user role');
      }

      return data?.role || null;
    },
    enabled: !!session?.user, // Only run the query if a user is authenticated
    staleTime: 5 * 60 * 1000, // Data is considered fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cached data will be garbage collected after 10 minutes of inactivity
  });
}