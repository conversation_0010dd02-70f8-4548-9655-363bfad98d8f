# Energieausweis Webanwendung

## Projektziel

Das Ziel dieses Projekts ist die Entwicklung einer Webanwendung, die es Endanwendern ermöglicht, Daten für einen Energieausweis über eine Reihe von Online-Formularen einzugeben. Nach der Eingabe erhalten die Nutzer eine tabellarische Zusammenfassung und können den Energieausweis über eine Stripe-Checkout-Integration "kaufen". Administratoren erhalten ein separates Dashboard zur Datenverwaltung und zum CSV-Export der erfassten Energieausweisdaten.

## Kernfunktionalitäten

- **Dateneingabe durch Nutzer**: Umfangreiche Formulare zur Erfassung aller relevanten Gebäudedaten, Kundendaten, Heizungs-, Trinkwarmwasser-, Lüftungs- und Verbrauchsdaten.
- **Upload-Möglichkeit**: Für Gebäudebilder und die letzten drei Verbrauchsrechnungen.
- **Datenzusammenfassung**: Anzeige einer tabellarischen Übersicht aller eingegebenen Daten vor dem Kauf.
- **Kaufabwicklung**: Integration von Stripe Checkout zur Bezahlung des Energieausweises.
- **Benutzerauthentifizierung**: Login/Logout-Funktionalität für Endanwender und Administratoren mittels Supabase Auth.
- **Admin-Dashboard**: Anzeige der erfassten Energieausweis-Datensätze und Funktion zum Export der Daten als CSV-Datei.

## Technische Spezifikationen

- **Frontend-Framework**: React.js
- **Programmiersprache**: TypeScript
- **Build-Tool**: Vite
- **Styling**: Tailwind CSS
- **Routing**: Tanstack Router
- **State Management & Datenabruf**: Tanstack Query
- **Formular-Management**: Tanstack Forms
- **Backend-as-a-Service (BaaS)**: Supabase (Authentifizierung, Datenbank, Storage)
- **Zahlungsdienstleister**: Stripe (Checkout)

## Aktueller Stand der Implementierung

- **HomePage**: Eine Willkommensseite, die den Nutzern die Möglichkeit bietet, einen Energieausweis zu erstellen oder sich anzumelden.
- **AdminPage**: Ein Grundgerüst für das Admin-Dashboard, das die Verwaltung der erfassten Energieausweisdaten ermöglicht. Die vollständige Implementierung des Dashboards und der Zugriffsrechte ist noch in Arbeit.

Dieses Projekt wird iterativ entwickelt, wobei der Fokus auf einer benutzerfreundlichen Oberfläche und einer robusten Datenverarbeitung liegt.
