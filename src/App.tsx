import { RouterProvider } from '@tanstack/react-router';
import { router } from './routes';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { CertificateProvider } from './contexts/CertificateContext';
import { useEffect } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Create a client
const queryClient = new QueryClient();

function AppWithAuth() {
  const { user, session, loading } = useAuth();

  useEffect(() => {
    router.update({
      context: {
        user,
        session,
        loading,
      },
    });
  }, [user, session, loading]);

  // Optional: Show a loading indicator while auth state is being determined
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="w-16 h-16 border-t-4 border-green-500 border-solid rounded-full animate-spin mx-auto"></div>
          <p className="mt-4 text-gray-600">Wird geladen...</p>
        </div>
      </div>
    );
  }

  return <RouterProvider router={router} />;
}

function App() {
  return (
    <AuthProvider>
      <QueryClientProvider client={queryClient}>
        <CertificateProvider>
          <AppWithAuth />
        </CertificateProvider>
      </QueryClientProvider>
    </AuthProvider>
  );
}

export default App;
