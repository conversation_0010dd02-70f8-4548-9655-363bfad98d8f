import { supabase } from '../lib/supabase';

/**
 * Manual fix utility for the specific failed transfer case
 * This script can be run to manually transfer files for the failed case
 */

interface ManualTransferResult {
  success: boolean;
  error?: string;
  transferredFiles?: number;
  failedFiles?: string[];
  details?: string[];
}

/**
 * Manually fix the specific failed transfer case
 * Source: 159dc937-256c-4fdc-8a74-524eeefff5c6
 * Target: 76961f81-1650-41d7-9116-0fb510683574
 * Certificate: f65a2269-1823-455e-8ed6-fa49370b0b8c
 */
export const fixFailedTransferCase = async (): Promise<ManualTransferResult> => {
  const sourceUserId = '159dc937-256c-4fdc-8a74-524eeefff5c6';
  const targetUserId = '76961f81-1650-41d7-9116-0fb510683574';
  const certificateId = 'f65a2269-1823-455e-8ed6-fa49370b0b8c';

  console.log('🔧 Starting manual fix for failed transfer case...');
  console.log(`📋 Source: ${sourceUserId}`);
  console.log(`📋 Target: ${targetUserId}`);
  console.log(`📋 Certificate: ${certificateId}`);

  try {
    // First, verify the certificate ownership is correct
    const { data: certData, error: certError } = await supabase
      .from('energieausweise')
      .select('user_id, certificate_type, status')
      .eq('id', certificateId)
      .single();

    if (certError) {
      return {
        success: false,
        error: `Failed to verify certificate: ${certError.message}`
      };
    }

    console.log('📊 Certificate status:', certData);

    if (certData.user_id !== targetUserId) {
      return {
        success: false,
        error: `Certificate ownership mismatch. Expected: ${targetUserId}, Found: ${certData.user_id}`
      };
    }

    // List files in source directory
    const { data: sourceFiles, error: listError } = await supabase.storage
      .from('certificateuploads')
      .list(`${sourceUserId}/${certificateId}`);

    if (listError) {
      return {
        success: false,
        error: `Failed to list source files: ${listError.message}`
      };
    }

    if (!sourceFiles || sourceFiles.length === 0) {
      return {
        success: true,
        transferredFiles: 0,
        details: ['No files found in source directory - transfer may have already completed']
      };
    }

    console.log(`📁 Found ${sourceFiles.length} files to transfer:`, sourceFiles.map(f => f.name));

    // Check if files already exist in target directory
    const { data: targetFiles } = await supabase.storage
      .from('certificateuploads')
      .list(`${targetUserId}/${certificateId}`);

    const existingFiles = targetFiles?.map(f => f.name) || [];
    console.log(`📁 Existing files in target directory: ${existingFiles.length}`, existingFiles);

    const details: string[] = [];
    const failedFiles: string[] = [];
    let transferredCount = 0;

    // Use the Edge Function for transfer
    console.log('🚀 Attempting transfer via Edge Function...');
    
    const { data: edgeFunctionResult, error: edgeFunctionError } = await supabase.functions.invoke('transfer-certificate-files', {
      body: {
        sourceUserId,
        targetUserId,
        certificateId
      }
    });

    if (edgeFunctionError) {
      console.error('❌ Edge Function failed:', edgeFunctionError);
      details.push(`Edge Function error: ${edgeFunctionError.message}`);
      
      // Fallback to manual approach
      console.log('🔄 Falling back to manual transfer approach...');
      
      for (const file of sourceFiles) {
        try {
          const sourceFilePath = `${sourceUserId}/${certificateId}/${file.name}`;
          const targetFilePath = `${targetUserId}/${certificateId}/${file.name}`;

          // Check if file already exists in target
          if (existingFiles.includes(file.name)) {
            console.log(`⏭️ File ${file.name} already exists in target, skipping`);
            details.push(`Skipped ${file.name} - already exists in target`);
            continue;
          }

          console.log(`📄 Attempting to transfer: ${file.name}`);

          // Try to download from source (this will likely fail due to RLS)
          const { data: fileData, error: downloadError } = await supabase.storage
            .from('certificateuploads')
            .download(sourceFilePath);

          if (downloadError) {
            console.error(`❌ Download failed for ${file.name}:`, downloadError.message);
            failedFiles.push(file.name);
            details.push(`Download failed for ${file.name}: ${downloadError.message}`);
            continue;
          }

          // Try to upload to target (this will also likely fail due to RLS)
          const { error: uploadError } = await supabase.storage
            .from('certificateuploads')
            .upload(targetFilePath, fileData, { upsert: false });

          if (uploadError) {
            console.error(`❌ Upload failed for ${file.name}:`, uploadError.message);
            failedFiles.push(file.name);
            details.push(`Upload failed for ${file.name}: ${uploadError.message}`);
            continue;
          }

          transferredCount++;
          details.push(`Successfully transferred: ${file.name}`);
          console.log(`✅ Successfully transferred: ${file.name}`);

        } catch (error: any) {
          console.error(`❌ Error transferring ${file.name}:`, error);
          failedFiles.push(file.name);
          details.push(`Error transferring ${file.name}: ${error.message}`);
        }
      }
    } else {
      // Edge Function succeeded
      console.log('✅ Edge Function completed:', edgeFunctionResult);
      return {
        success: edgeFunctionResult.success,
        transferredFiles: edgeFunctionResult.transferredFiles,
        failedFiles: edgeFunctionResult.failedFiles,
        error: edgeFunctionResult.error,
        details: [`Edge Function result: ${JSON.stringify(edgeFunctionResult)}`]
      };
    }

    const success = failedFiles.length === 0;
    
    return {
      success,
      transferredFiles: transferredCount,
      failedFiles: failedFiles.length > 0 ? failedFiles : undefined,
      error: success ? undefined : `Failed to transfer ${failedFiles.length} files`,
      details
    };

  } catch (error: any) {
    console.error('❌ Unexpected error during manual fix:', error);
    return {
      success: false,
      error: error.message || 'Unexpected error during manual fix',
      details: [`Unexpected error: ${error.message}`]
    };
  }
};

/**
 * Diagnostic function to check the current state of the failed transfer case
 */
export const diagnoseFailedTransferCase = async (): Promise<{
  certificateOwnership: any;
  sourceFiles: any[];
  targetFiles: any[];
  storagePermissions: string;
}> => {
  const sourceUserId = '159dc937-256c-4fdc-8a74-524eeefff5c6';
  const targetUserId = '76961f81-1650-41d7-9116-0fb510683574';
  const certificateId = 'f65a2269-1823-455e-8ed6-fa49370b0b8c';

  console.log('🔍 Diagnosing failed transfer case...');

  // Check certificate ownership
  const { data: certData, error: certError } = await supabase
    .from('energieausweise')
    .select('user_id, certificate_type, status, created_at, updated_at')
    .eq('id', certificateId)
    .single();

  // Check source files
  const { data: sourceFiles, error: sourceError } = await supabase.storage
    .from('certificateuploads')
    .list(`${sourceUserId}/${certificateId}`);

  // Check target files
  const { data: targetFiles, error: targetError } = await supabase.storage
    .from('certificateuploads')
    .list(`${targetUserId}/${certificateId}`);

  // Check current user permissions
  const { data: { user } } = await supabase.auth.getUser();
  
  return {
    certificateOwnership: certError ? { error: certError.message } : certData,
    sourceFiles: sourceError ? [{ error: sourceError.message }] : (sourceFiles || []),
    targetFiles: targetError ? [{ error: targetError.message }] : (targetFiles || []),
    storagePermissions: `Current user: ${user?.id || 'none'}, Anonymous: ${user?.is_anonymous || false}`
  };
};
