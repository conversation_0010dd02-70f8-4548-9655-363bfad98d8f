import React, { useState, useEffect } from 'react';
import { useField } from '@tanstack/react-form';
import { formatValidationError } from '../../utils/formValidation';
import type { CertificateType } from '../../utils/certificateTypeMapping';

interface BaujahrFieldWithHintProps {
  name: string;
  label: string;
  placeholder?: string;
  required?: boolean;
  form: any;
  helpText?: string;
  certificateType?: CertificateType | null;
}

/**
 * Enhanced Baujahr field component with contextual hint for WG/V certificates
 * Shows a warning when building year is before 1977 for WG/V certificate type,
 * considering modernization year to determine if WG/V certificate is still appropriate
 */
export const BaujahrFieldWithHint: React.FC<BaujahrFieldWithHintProps> = ({
  name,
  label,
  placeholder = '',
  required = true,
  form,
  helpText,
  certificateType
}) => {
  const field = useField({
    form,
    name: name as any,
  });

  // Access the Modernisierung field to consider modernization year
  const modernisierungField = useField({
    form,
    name: 'Modernisierung' as any,
  });

  // Use local state for the input value to prevent focus loss
  const [localValue, setLocalValue] = useState<string>(String(field.state.value ?? ''));
  const [showHint, setShowHint] = useState(false);

  // Update local value when field value changes (from external sources)
  useEffect(() => {
    setLocalValue(String(field.state.value ?? ''));
  }, [field.state.value]);

  // Enhanced logic to check if we should show the hint for WG/V certificates
  // considering both building year and modernization year
  useEffect(() => {
    if (certificateType === 'WG/V' && localValue) {
      const buildingYear = parseInt(localValue, 10);

      // Only proceed if building year is valid and before 1977
      if (!isNaN(buildingYear) && buildingYear < 1977) {
        const modernizationValue = String(modernisierungField.state.value ?? '').trim();

        if (modernizationValue === '') {
          // No modernization year provided - show hint
          setShowHint(true);
        } else {
          const modernizationYear = parseInt(modernizationValue, 10);

          if (!isNaN(modernizationYear)) {
            // Modernization year is valid
            if (modernizationYear >= 1977) {
              // Modernization after 1977 - do NOT show hint
              setShowHint(false);
            } else {
              // Modernization before 1977 - show hint
              setShowHint(true);
            }
          } else {
            // Invalid modernization year - show hint to be safe
            setShowHint(true);
          }
        }
      } else {
        // Building year >= 1977 or invalid - do NOT show hint
        setShowHint(false);
      }
    } else {
      // Not WG/V certificate or no building year - do NOT show hint
      setShowHint(false);
    }
  }, [localValue, certificateType, modernisierungField.state.value]);

  // Handle input changes with debouncing
  const handleChange = (value: string) => {
    setLocalValue(value);
    
    // Update the form field value
    field.handleChange(value);
  };

  // Check if field has errors
  const hasErrors = field.state.meta.errors.length > 0;
  const errors: (string | undefined)[] = field.state.meta.errors || [];

  // Format errors for better display
  const formattedErrors = errors
    .filter((error): error is string => typeof error === 'string')
    .map((error: string) => formatValidationError(error, name));

  return (
    <div className="mb-4">
      <label htmlFor={name} className="block text-sm font-medium text-gray-700 mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <input
        id={name}
        name={name}
        type="text"
        value={localValue}
        onChange={(e) => handleChange(e.target.value)}
        onBlur={field.handleBlur}
        placeholder={placeholder}
        className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 ${
          hasErrors ? 'border-red-500' : 'border-gray-300'
        }`}
        aria-invalid={hasErrors}
        aria-describedby={
          hasErrors ? `${name}-error` : 
          helpText ? `${name}-help` : 
          showHint ? `${name}-hint` : undefined
        }
      />
      
      {/* Help text */}
      {helpText && !hasErrors && !showHint && (
        <p id={`${name}-help`} className="mt-1 text-sm text-gray-500">
          {helpText}
        </p>
      )}

      {/* Contextual hint for WG/V certificates with building year < 1977 */}
      {showHint && !hasErrors && (
        <div id={`${name}-hint`} className="mt-2 p-3 bg-amber-50 border border-amber-200 rounded-md">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-amber-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-amber-800">
                Hinweis zum Zertifikatstyp
              </h3>
              <div className="mt-2 text-sm text-amber-700">
                <p>
                  Ihr Gebäude wurde vor 1977 errichtet. Ohne wesentliche energetische
                  Modernisierungen nach 1977 benötigen Sie möglicherweise einen
                  <strong> Bedarfsausweis (WG/B)</strong> anstelle eines Verbrauchsausweises (WG/V).
                </p>
                <p className="mt-1">
                  <strong>Hinweis:</strong> Falls Sie eine energetische Sanierung nach 1977 durchgeführt haben,
                  tragen Sie das Jahr der Modernisierung ein. Modernisierungen ab 1977 können die
                  Verwendung eines Verbrauchsausweises (WG/V) rechtfertigen.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Error messages */}
      {hasErrors && (
        <div id={`${name}-error`} className="mt-1">
          {formattedErrors.map((error, index) => (
            <p key={index} className="text-sm text-red-500">
              {error}
            </p>
          ))}
        </div>
      )}
    </div>
  );
};
