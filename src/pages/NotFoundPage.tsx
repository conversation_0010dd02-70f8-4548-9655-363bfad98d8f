import { Link } from '@tanstack/react-router';

export const NotFoundPage = () => {
  return (
    <div className="max-w-md mx-auto text-center">
      <h1 className="text-6xl font-bold text-gray-800 mb-4">404</h1>
      <h2 className="text-2xl font-semibold text-gray-700 mb-6">Seite nicht gefunden</h2>
      <p className="text-lg text-gray-600 mb-8">
        Die angeforderte Seite existiert nicht oder wurde verschoben.
      </p>
      <Link
        to="/"
        className="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-6 rounded-lg inline-block transition-colors"
      >
        Zurück zur Startseite
      </Link>
    </div>
  );
};
