-- Create payment_attempts table for comprehensive payment lifecycle tracking
-- This table tracks all payment attempts, including successful, failed, and abandoned sessions

CREATE TABLE IF NOT EXISTS payment_attempts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  certificate_id UUID REFERENCES energieausweise(id) ON DELETE CASCADE,
  stripe_session_id TEXT,
  stripe_payment_intent_id TEXT,
  attempt_status TEXT NOT NULL DEFAULT 'initiated' CHECK (
    attempt_status IN ('initiated', 'processing', 'abandoned', 'failed', 'succeeded', 'expired', 'disputed')
  ),
  payment_method TEXT, -- card, bank_transfer, etc.
  amount_cents INTEGER,
  currency TEXT DEFAULT 'eur',
  abandonment_reason TEXT,
  failure_reason TEXT,
  user_agent TEXT,
  ip_address INET,
  session_duration_seconds INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add comments for documentation
COMMENT ON TABLE payment_attempts IS 'Tracks all payment attempts for energy certificates including successful, failed, and abandoned sessions';
COMMENT ON COLUMN payment_attempts.attempt_status IS 'Payment attempt status: initiated, processing, abandoned, failed, succeeded, expired, disputed';
COMMENT ON COLUMN payment_attempts.abandonment_reason IS 'Reason for abandonment: user_cancelled, session_timeout, technical_error, etc.';
COMMENT ON COLUMN payment_attempts.failure_reason IS 'Specific failure reason from Stripe or internal systems';
COMMENT ON COLUMN payment_attempts.session_duration_seconds IS 'Time spent in payment session before completion/abandonment';

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_payment_attempts_certificate_id ON payment_attempts(certificate_id);
CREATE INDEX IF NOT EXISTS idx_payment_attempts_stripe_session ON payment_attempts(stripe_session_id);
CREATE INDEX IF NOT EXISTS idx_payment_attempts_status ON payment_attempts(attempt_status);
CREATE INDEX IF NOT EXISTS idx_payment_attempts_created_at ON payment_attempts(created_at);
CREATE INDEX IF NOT EXISTS idx_payment_attempts_completed_at ON payment_attempts(completed_at);

-- Create a composite index for analytics queries
CREATE INDEX IF NOT EXISTS idx_payment_attempts_analytics ON payment_attempts(attempt_status, created_at, certificate_id);

-- Enable RLS (Row Level Security)
ALTER TABLE payment_attempts ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can only see their own payment attempts
CREATE POLICY "Users can view own payment attempts" ON payment_attempts
  FOR SELECT USING (
    certificate_id IN (
      SELECT id FROM energieausweise WHERE user_id = auth.uid()
    )
  );

-- Users can insert their own payment attempts
CREATE POLICY "Users can insert own payment attempts" ON payment_attempts
  FOR INSERT WITH CHECK (
    certificate_id IN (
      SELECT id FROM energieausweise WHERE user_id = auth.uid()
    )
  );

-- Users can update their own payment attempts
CREATE POLICY "Users can update own payment attempts" ON payment_attempts
  FOR UPDATE USING (
    certificate_id IN (
      SELECT id FROM energieausweise WHERE user_id = auth.uid()
    )
  );

-- Admins can see all payment attempts
CREATE POLICY "Admins can view all payment attempts" ON payment_attempts
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

-- Create a function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_payment_attempts_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic updated_at updates
CREATE TRIGGER payment_attempts_updated_at_trigger
  BEFORE UPDATE ON payment_attempts
  FOR EACH ROW
  EXECUTE FUNCTION update_payment_attempts_updated_at();

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON payment_attempts TO authenticated;
