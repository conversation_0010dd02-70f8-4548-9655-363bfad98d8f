# Testing File Transfer During Account Conversion

## Overview
This document provides instructions for testing the enhanced account conversion process that includes file storage directory renaming.

## Test Scenarios

### 1. **Anonymous User Account Conversion**
Test the complete flow from anonymous user to registered user with file transfers.

#### Prerequisites:
- Anonymous user with uploaded files in a certificate
- Files should be in: `anonymousUserId/certificateId/fieldName_filename`

#### Test Steps:
1. Create an anonymous user session
2. Upload files to a certificate (building images, consumption bills)
3. Navigate to summary page and trigger account conversion
4. Complete the conversion process
5. Verify files are accessible in the new user directory

#### Expected Results:
- Files moved from `anonymousUserId/certificateId/` to `registeredUserId/certificateId/`
- All files remain accessible after conversion
- Database ownership updated correctly
- No orphaned files in anonymous user directory

### 2. **Login with Existing Account**
Test transferring certificate data to an existing account.

#### Test Steps:
1. Create anonymous user with certificate and files
2. Choose "Login with existing account" option
3. Enter credentials for existing account
4. Complete the transfer process

#### Expected Results:
- Files transferred to existing user's directory
- Certificate ownership transferred
- Files accessible under existing user account

### 3. **Alternative Email Registration**
Test using alternative email when original email conflicts.

#### Test Steps:
1. Create anonymous user with certificate and files
2. Try to register with email that already exists
3. Choose alternative email option
4. Complete registration with new email

#### Expected Results:
- Files transferred to new user directory
- Account created with alternative email
- All files remain accessible

## Manual Testing

### Using Browser Developer Tools:
```javascript
// Import test utilities in browser console
import { testFileTransfer, verifyFilesAccessible, logTestResults } from './src/utils/testFileTransfer';

// Test file transfer between users
const result = await testFileTransfer('anonymous-user-id', 'registered-user-id', 'certificate-id');
logTestResults(result);

// Verify files are accessible after transfer
const verification = await verifyFilesAccessible('registered-user-id', 'certificate-id');
console.log('File accessibility:', verification);
```

### Using Supabase Dashboard:
1. **Before Conversion**: Check Storage > certificateuploads > `anonymousUserId/certificateId/`
2. **After Conversion**: Verify files moved to `registeredUserId/certificateId/`
3. **Database Check**: Verify `energieausweise` table shows correct `user_id`

## Automated Testing

### Unit Tests (Recommended):
```typescript
// Example test structure
describe('Account Conversion File Transfer', () => {
  test('should transfer files during anonymous user conversion', async () => {
    // Setup anonymous user with files
    // Perform conversion
    // Verify file transfer
    // Verify accessibility
  });

  test('should rollback files on database transfer failure', async () => {
    // Setup scenario that causes database failure
    // Verify files are rolled back to original location
  });

  test('should handle partial file transfer failures', async () => {
    // Setup scenario with some file transfer failures
    // Verify error handling and reporting
  });
});
```

## Monitoring and Debugging

### Console Logs to Watch:
- `🔄 Starting file transfer: userId1/certId -> userId2/certId`
- `📁 Found X files to transfer: [file1, file2, ...]`
- `📄 Transferring: source -> target`
- `✅ Successfully transferred: filename`
- `❌ Failed to copy file: error details`
- `✅ File transfer completed successfully: X files transferred`

### Error Scenarios to Test:
1. **Storage Permission Issues**: Verify proper error handling
2. **Network Interruptions**: Test resilience during file operations
3. **Large File Transfers**: Verify timeout handling
4. **Duplicate File Names**: Test conflict resolution
5. **Storage Quota Exceeded**: Verify graceful failure

## Performance Considerations

### File Transfer Optimization:
- Files are transferred sequentially to avoid overwhelming storage API
- Each file is verified after transfer before proceeding
- Original files are only deleted after successful copy verification
- Rollback operations are performed if database transfer fails

### Expected Transfer Times:
- Small files (< 1MB): ~1-2 seconds per file
- Medium files (1-5MB): ~3-5 seconds per file
- Large files (> 5MB): ~5-10 seconds per file

## Troubleshooting

### Common Issues:
1. **Files not found after conversion**: Check console logs for transfer errors
2. **Partial transfers**: Review failed files list in transfer results
3. **Permission errors**: Verify Supabase RLS policies allow file operations
4. **Timeout errors**: Check network connectivity and file sizes

### Recovery Steps:
1. Check Supabase Storage dashboard for file locations
2. Review browser console for detailed error logs
3. Use test utilities to verify file accessibility
4. Contact support with specific error messages and user IDs

## Success Criteria

✅ **Complete Success**:
- All files transferred successfully
- Database ownership updated
- Files accessible in new location
- No orphaned files in old location
- User experience smooth with appropriate feedback

✅ **Acceptable Partial Success**:
- Most files transferred successfully
- Failed files clearly identified and logged
- User notified of any issues
- Core functionality remains intact

❌ **Failure Scenarios**:
- Files lost or inaccessible
- Database inconsistency
- User unable to access their data
- Silent failures without user notification
