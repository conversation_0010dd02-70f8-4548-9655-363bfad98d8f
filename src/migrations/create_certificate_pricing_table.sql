-- Create certificate pricing table for dynamic price management
-- This table stores pricing information for different certificate types
-- and integrates with Stripe Products/Prices for checkout sessions

CREATE TABLE IF NOT EXISTS certificate_pricing (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  certificate_type TEXT NOT NULL CHECK (
    certificate_type IN ('WG/V', 'WG/B', 'NWG/V')
  ),
  price_cents INTEGER NOT NULL CHECK (price_cents > 0),
  currency TEXT NOT NULL DEFAULT 'eur' CHECK (currency IN ('eur', 'usd')),
  stripe_product_id TEXT,
  stripe_price_id TEXT,
  is_active BOOLEAN NOT NULL DEFAULT true,
  display_name TEXT NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create unique constraint to ensure only one active price per certificate type
CREATE UNIQUE INDEX IF NOT EXISTS idx_certificate_pricing_active_type 
ON certificate_pricing(certificate_type) 
WHERE is_active = true;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_certificate_pricing_type ON certificate_pricing(certificate_type);
CREATE INDEX IF NOT EXISTS idx_certificate_pricing_active ON certificate_pricing(is_active);
CREATE INDEX IF NOT EXISTS idx_certificate_pricing_stripe_product ON certificate_pricing(stripe_product_id);
CREATE INDEX IF NOT EXISTS idx_certificate_pricing_stripe_price ON certificate_pricing(stripe_price_id);

-- Add comments for documentation
COMMENT ON TABLE certificate_pricing IS 'Stores pricing information for energy certificate types with Stripe integration';
COMMENT ON COLUMN certificate_pricing.certificate_type IS 'Type of energy certificate: WG/V, WG/B, or NWG/V';
COMMENT ON COLUMN certificate_pricing.price_cents IS 'Price in cents (e.g., 4900 for €49.00)';
COMMENT ON COLUMN certificate_pricing.stripe_product_id IS 'Stripe Product ID for this certificate type';
COMMENT ON COLUMN certificate_pricing.stripe_price_id IS 'Stripe Price ID for checkout sessions';
COMMENT ON COLUMN certificate_pricing.is_active IS 'Whether this pricing is currently active (only one active price per type)';

-- Insert default pricing data
INSERT INTO certificate_pricing (
  certificate_type, 
  price_cents, 
  display_name, 
  description,
  is_active
) VALUES 
(
  'WG/V', 
  4900, 
  'Wohngebäude-Verbrauchsausweis', 
  'Für Wohngebäude basierend auf dem tatsächlichen Energieverbrauch',
  true
),
(
  'WG/B', 
  4900, 
  'Wohngebäude-Bedarfsausweis', 
  'Für Wohngebäude basierend auf dem berechneten Energiebedarf',
  true
),
(
  'NWG/V', 
  4900, 
  'Nicht-Wohngebäude-Verbrauchsausweis', 
  'Für Nicht-Wohngebäude basierend auf dem tatsächlichen Energieverbrauch',
  true
);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_certificate_pricing_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update updated_at
CREATE TRIGGER trigger_certificate_pricing_updated_at
  BEFORE UPDATE ON certificate_pricing
  FOR EACH ROW
  EXECUTE FUNCTION update_certificate_pricing_updated_at();

-- Add RLS (Row Level Security) policies for admin access
ALTER TABLE certificate_pricing ENABLE ROW LEVEL SECURITY;

-- Policy: Allow read access to all authenticated users
CREATE POLICY "Allow read access to certificate pricing" ON certificate_pricing
  FOR SELECT TO authenticated
  USING (true);

-- Policy: Allow admin users to manage pricing
CREATE POLICY "Allow admin users to manage certificate pricing" ON certificate_pricing
  FOR ALL TO authenticated
  USING (is_admin_user());

-- Grant necessary permissions
GRANT SELECT ON certificate_pricing TO authenticated;
GRANT ALL ON certificate_pricing TO service_role;
