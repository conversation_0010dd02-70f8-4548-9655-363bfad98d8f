import { z } from 'zod';

/**
 * Utility functions for handling form validation errors
 */

/**
 * Maps Zod validation errors to field-specific error messages
 * @param zodError - The ZodError instance from validation
 * @returns Object with field names as keys and error messages as values
 */
export function mapZodErrorsToFields(zodError: z.ZodError): Record<string, string[]> {
  const fieldErrors: Record<string, string[]> = {};
  
  // Get flattened field errors from Zod
  const flattenedErrors = zodError.flatten().fieldErrors;
  
  // Convert to our expected format
  Object.entries(flattenedErrors).forEach(([fieldName, errors]) => {
    if (errors && errors.length > 0) {
      fieldErrors[fieldName] = errors;
    }
  });
  
  return fieldErrors;
}

/**
 * Sets field-level errors in a Tanstack Form instance
 * @param form - The Tanstack Form instance
 * @param fieldErrors - Object with field names as keys and error arrays as values
 */
export function setFormFieldErrors(form: any, fieldErrors: Record<string, string[]>) {
  Object.entries(fieldErrors).forEach(([fieldName, errors]) => {
    if (errors && errors.length > 0) {
      // Set the error for this specific field
      form.setFieldMeta(fieldName, (prev: any) => ({
        ...prev,
        errors: errors,
      }));
    }
  });
}

/**
 * Clears all field errors in a Tanstack Form instance
 * @param form - The Tanstack Form instance
 * @param fieldNames - Array of field names to clear errors for
 */
export function clearFormFieldErrors(form: any, fieldNames: string[]) {
  fieldNames.forEach(fieldName => {
    form.setFieldMeta(fieldName, (prev: any) => ({
      ...prev,
      errors: [],
    }));
  });
}

/**
 * Handles Zod validation errors by mapping them to form field errors
 * @param zodError - The ZodError instance
 * @param form - The Tanstack Form instance
 * @returns Object containing field errors and a summary message
 */
export function handleZodValidationError(zodError: z.ZodError, form: any) {
  const fieldErrors = mapZodErrorsToFields(zodError);
  setFormFieldErrors(form, fieldErrors);
  
  // Create a summary of errors for display
  const errorCount = Object.keys(fieldErrors).length;
  const summaryMessage = `${errorCount} Feld${errorCount > 1 ? 'er' : ''} ${errorCount > 1 ? 'enthalten' : 'enthält'} Fehler. Bitte korrigieren Sie die markierten Eingaben.`;
  
  return {
    fieldErrors,
    summaryMessage,
    hasErrors: errorCount > 0
  };
}

/**
 * Gets a user-friendly error message for a specific validation error
 * @param error - The error message from Zod
 * @param fieldName - The name of the field (for context)
 * @returns Formatted error message
 */
export function formatValidationError(error: string, fieldName?: string): string {
  // Common error message mappings for better UX
  const errorMappings: Record<string, string> = {
    'Required': 'Dieses Feld ist erforderlich',
    'Invalid email': 'Bitte geben Sie eine gültige E-Mail-Adresse ein',
    'String must contain at least 1 character(s)': 'Dieses Feld darf nicht leer sein',
    'String must contain at least 5 character(s)': 'Mindestens 5 Zeichen erforderlich',
    'Invalid': 'Ungültige Eingabe',
  };
  
  // Check for exact matches first
  if (errorMappings[error]) {
    return errorMappings[error];
  }
  
  // Check for partial matches
  for (const [key, value] of Object.entries(errorMappings)) {
    if (error.includes(key.toLowerCase()) || error.toLowerCase().includes(key.toLowerCase())) {
      return value;
    }
  }
  
  // If no mapping found, include field name in the error message if available
  if (fieldName) {
    return `Fehler im Feld "${fieldName}": ${error}`;
  }
  
  // Return the original error if no mapping found and no field name provided
  return error;
}
