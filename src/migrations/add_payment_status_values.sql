-- Add new payment status values to support enhanced webhook handling
-- This migration adds support for additional payment statuses beyond the current: unpaid, paid, expired

-- Add comment to document the expanded payment status values
COMMENT ON COLUMN energieausweise.payment_status IS 'Payment status: unpaid (default), paid (successful), expired (session expired), failed (payment failed), disputed (chargeback/dispute created)';

-- Update any existing NULL payment_status values to 'unpaid' for consistency
UPDATE energieausweise 
SET payment_status = 'unpaid' 
WHERE payment_status IS NULL;

-- Add a check constraint to ensure only valid payment status values are used
-- Note: We're not adding a strict constraint here to maintain flexibility
-- but documenting the expected values for reference

-- Create an index on payment_status for better query performance
CREATE INDEX IF NOT EXISTS idx_energieausweise_payment_status ON energieausweise(payment_status);

-- Create an index on stripe_checkout_session_id for webhook lookups
CREATE INDEX IF NOT EXISTS idx_energieausweise_stripe_session ON energieausweise(stripe_checkout_session_id);

-- Add comment explaining the payment status lifecycle
COMMENT ON TABLE energieausweise IS 'Table for storing energy certificate data. Multiple certificates per user are supported. Payment status lifecycle: unpaid -> paid/failed/expired/disputed';
