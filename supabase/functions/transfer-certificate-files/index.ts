import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';

interface TransferRequest {
  sourceUserId: string;
  targetUserId: string;
  certificateId: string;
}

interface TransferResponse {
  success: boolean;
  error?: string;
  transferredFiles?: number;
  failedFiles?: string[];
  totalFiles?: number;
}

Deno.serve(async (req: Request) => {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { status: 405, headers: { 'Content-Type': 'application/json' } }
    );
  }

  try {
    // Parse request body
    const { sourceUserId, targetUserId, certificateId }: TransferRequest = await req.json();

    // Validate inputs
    if (!sourceUserId || !targetUserId || !certificateId) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Missing required parameters: sourceUserId, targetUserId, certificateId' 
        }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Check if source and target are the same
    if (sourceUserId === targetUserId) {
      return new Response(
        JSON.stringify({ 
          success: true, 
          message: 'Source and target users are the same, no transfer needed',
          transferredFiles: 0 
        }),
        { status: 200, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Create Supabase client with service role key (bypasses RLS)
    const supabaseServiceRole = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    console.log(`🔄 Starting file transfer: ${sourceUserId}/${certificateId} -> ${targetUserId}/${certificateId}`);

    // List all files in source directory
    const { data: sourceFiles, error: listError } = await supabaseServiceRole.storage
      .from('certificateuploads')
      .list(`${sourceUserId}/${certificateId}`);

    if (listError) {
      console.error('❌ Error listing source files:', listError);
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: `Failed to list source files: ${listError.message}` 
        }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }

    if (!sourceFiles || sourceFiles.length === 0) {
      console.log('ℹ️ No files found in source directory');
      return new Response(
        JSON.stringify({ 
          success: true, 
          message: 'No files to transfer',
          transferredFiles: 0,
          totalFiles: 0 
        }),
        { status: 200, headers: { 'Content-Type': 'application/json' } }
      );
    }

    console.log(`📊 Found ${sourceFiles.length} files to transfer`);

    let transferredCount = 0;
    const failedFiles: string[] = [];

    // Transfer each file
    for (const file of sourceFiles) {
      try {
        const sourceFilePath = `${sourceUserId}/${certificateId}/${file.name}`;
        const targetFilePath = `${targetUserId}/${certificateId}/${file.name}`;

        console.log(`📄 Transferring: ${sourceFilePath} -> ${targetFilePath}`);

        // Download the file from source
        const { data: fileData, error: downloadError } = await supabaseServiceRole.storage
          .from('certificateuploads')
          .download(sourceFilePath);

        if (downloadError || !fileData) {
          console.error(`❌ Failed to download file ${file.name}:`, downloadError);
          failedFiles.push(file.name);
          continue;
        }

        // Upload the file to target location
        const { error: uploadError } = await supabaseServiceRole.storage
          .from('certificateuploads')
          .upload(targetFilePath, fileData, {
            upsert: false // Don't overwrite existing files
          });

        if (uploadError) {
          console.error(`❌ Failed to upload file ${file.name}:`, uploadError);
          failedFiles.push(file.name);
          continue;
        }

        // Verify the copy was successful
        const { data: verifyFiles } = await supabaseServiceRole.storage
          .from('certificateuploads')
          .list(`${targetUserId}/${certificateId}`, {
            search: file.name
          });

        if (!verifyFiles || verifyFiles.length === 0) {
          console.error(`❌ Copy verification failed for ${file.name}`);
          failedFiles.push(file.name);
          continue;
        }

        // Delete the original file
        const { error: deleteError } = await supabaseServiceRole.storage
          .from('certificateuploads')
          .remove([sourceFilePath]);

        if (deleteError) {
          console.warn(`⚠️ Failed to delete original file ${sourceFilePath}, but copy succeeded:`, deleteError);
          // Don't count this as a failure since the copy succeeded
        }

        transferredCount++;
        console.log(`✅ Successfully transferred: ${file.name}`);

      } catch (error) {
        console.error(`❌ Error transferring file ${file.name}:`, error);
        failedFiles.push(file.name);
      }
    }

    const success = failedFiles.length === 0;
    const response: TransferResponse = {
      success,
      transferredFiles: transferredCount,
      totalFiles: sourceFiles.length,
      ...(failedFiles.length > 0 && { failedFiles })
    };

    if (success) {
      console.log(`✅ File transfer completed successfully: ${transferredCount} files transferred`);
    } else {
      console.error(`❌ File transfer completed with errors: ${transferredCount}/${sourceFiles.length} files transferred`);
      response.error = `Failed to transfer ${failedFiles.length} files: ${failedFiles.join(', ')}`;
    }

    return new Response(
      JSON.stringify(response),
      { 
        status: success ? 200 : 500, 
        headers: { 'Content-Type': 'application/json' } 
      }
    );

  } catch (error: any) {
    console.error('❌ Unexpected error during file transfer:', error);
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message || 'Unexpected error during file transfer' 
      }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
});
