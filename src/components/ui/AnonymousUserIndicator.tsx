import { useAuth } from '../../contexts/AuthContext';

interface AnonymousUserIndicatorProps {
  className?: string;
  showFullMessage?: boolean;
}

export const AnonymousUserIndicator = ({ 
  className = '', 
  showFullMessage = true 
}: AnonymousUserIndicatorProps) => {
  const { isAnonymous } = useAuth();

  if (!isAnonymous) {
    return null;
  }

  return (
    <div className={`p-4 bg-blue-50 border border-blue-200 rounded-lg ${className}`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium text-blue-800">
            Schnellstart ohne Registrierung
          </h3>
          {showFullMessage && (
            <div className="mt-2 text-sm text-blue-700">
              <p>
                Sie können sofort mit der Dateneingabe beginnen! Ihre E-Mail-Adresse wird benötigt, 
                um Ihnen später den fertigen Energieausweis zuzusenden. Am Ende des Prozesses können 
                Sie optional ein Konto erstellen, um Ihre Zertifikate zu verwalten.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
