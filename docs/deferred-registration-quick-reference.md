# Deferred Registration - Quick Reference Guide

## Overview
The deferred registration system allows users to create energy certificates without upfront registration, using Supabase anonymous authentication with optional account conversion at payment stage.

## Key Components

### 1. Authentication Context Extensions
```typescript
// New properties and methods in AuthContext
interface AuthContextType {
  isAnonymous: boolean;                    // Track anonymous state
  signInAnonymously: () => Promise<any>;   // Anonymous sign-in
  linkIdentity: (email, password) => Promise<any>; // Account conversion
}
```

### 2. New Hooks and Components
- `useAnonymousAuth` - Manages anonymous authentication flows
- `AnonymousUserIndicator` - Shows anonymous user status
- `AccountConversionModal` - Handles password creation for account upgrade

### 3. Route Protection
- `checkAuthOrAnonymous()` - Allows both anonymous and permanent users
- Applied to all `/erfassen/*` routes for certificate data entry

## Quick Implementation Checklist

### ✅ Supabase Configuration
- [x] Anonymous authentication enabled
- [x] Manual linking enabled
- [x] RLS policies support anonymous users

### ✅ Authentication Flow
- [x] Auto sign-in anonymously on certificate creation
- [x] Anonymous user state tracking
- [x] Account conversion at payment stage

### ✅ User Interface
- [x] Anonymous user indicators on data entry pages
- [x] Enhanced email field for anonymous users
- [x] Account conversion modal with password creation

### ✅ Data Management
- [x] Certificate ownership preserved during conversion
- [x] File uploads work for anonymous users
- [x] User ID consistency maintained

## Usage Examples

### Creating Certificate with Anonymous Auth
```typescript
const { createCertificateWithAuth } = useAnonymousAuth();

const handleCreate = async (type: string) => {
  const certificateId = await createCertificateWithAuth(type);
  if (certificateId) {
    navigate('/erfassen/objektdaten');
  }
};
```

### Showing Anonymous User Status
```typescript
import { AnonymousUserIndicator } from '../components/ui/AnonymousUserIndicator';

const MyPage = () => (
  <div>
    <AnonymousUserIndicator className="mb-4" />
    {/* Rest of page content */}
  </div>
);
```

### Account Conversion Flow
```typescript
const { isAnonymous } = useAuth();
const [showConversion, setShowConversion] = useState(false);

const handlePayment = () => {
  if (isAnonymous) {
    setShowConversion(true); // Show conversion modal
  } else {
    proceedToPayment(); // Direct to payment
  }
};
```

## User Journey Summary

1. **Homepage** → Certificate quiz → Auto anonymous sign-in
2. **Data Entry** → Email capture → Form completion with anonymous indicators
3. **Summary** → Legal consent → Payment button
4. **Account Conversion** → Optional password creation → Payment processing
5. **Completion** → Email notification → Success page

## Key Benefits

- **Reduced Friction**: No upfront registration required
- **User Choice**: Optional account creation at payment stage
- **Data Security**: RLS policies ensure data isolation
- **Seamless Experience**: Transparent authentication state transitions
- **Backward Compatibility**: Existing users unaffected

## Monitoring Points

- Anonymous user conversion rates
- Session abandonment rates
- Account conversion success/failure rates
- File upload success for anonymous users
- Payment completion rates by user type

## Troubleshooting

### Common Issues
1. **Anonymous sign-in fails** → Check Supabase project settings
2. **Account conversion fails** → Verify email format and existing account conflicts
3. **Data not persisting** → Ensure user ID consistency during conversion
4. **File uploads fail** → Check storage RLS policies and bucket permissions

### Debug Commands
```typescript
// Check current auth state
const { data: { user } } = await supabase.auth.getUser();
console.log('User:', user?.id, 'Anonymous:', user?.is_anonymous);

// Check certificate ownership
const { data } = await supabase
  .from('energieausweise')
  .select('*')
  .eq('user_id', user?.id);
console.log('User certificates:', data);
```

## Files Modified

### Core Authentication
- `src/contexts/AuthContext.tsx` - Extended with anonymous auth
- `src/utils/routeLoaders.ts` - Added `checkAuthOrAnonymous`
- `src/routes/index.tsx` - Updated route protection

### User Interface
- `src/pages/HomePage.tsx` - Auto anonymous sign-in
- `src/pages/erfassen/ObjektdatenPage.tsx` - Email capture & indicators
- `src/pages/erfassen/ZusammenfassungPage.tsx` - Account conversion

### New Components
- `src/components/auth/AccountConversionModal.tsx`
- `src/components/ui/AnonymousUserIndicator.tsx`
- `src/hooks/useAnonymousAuth.ts`
- `src/utils/accountConversion.ts`

### Navigation
- `src/layouts/Header.tsx` - Anonymous user navigation

This implementation provides a seamless user experience while maintaining security and data integrity throughout the anonymous-to-permanent user conversion process.
