# Email Notification System

## Overview

The email notification system automatically sends emails to customers and administrators when payment events occur in the energy certificate purchase workflow. The system uses Resend as the email service provider and logs all email activities to the database.

## Features

- **Automated Email Notifications**: Sends emails for payment success and failure events
- **Customer Notifications**: Confirmation emails for successful payments and retry instructions for failed payments
- **Admin Notifications**: Order notifications for successful payments and failure alerts for failed payments
- **Email Logging**: Comprehensive logging of all email activities with status tracking
- **Error Handling**: Graceful error handling that doesn't break the payment workflow
- **Resend Integration**: Uses Resend API for reliable email delivery

## Email Types

### Customer Emails

1. **Customer Success Email** (`customer_success`)
   - Sent when payment is successfully processed
   - Contains order confirmation details
   - Includes order number, certificate type, and building information

2. **Customer Failure Email** (`customer_failure`)
   - Sent when payment fails or expires
   - Contains retry instructions and support information
   - Includes link to retry payment

### Admin Emails

1. **Admin Success Email** (`admin_success`)
   - Sent when a new order is successfully paid
   - Contains comprehensive order details for processing
   - Includes customer information and building details

2. **Admin Failure Email** (`admin_failure`)
   - Sent when payment fails or expires
   - Contains order information for follow-up
   - Includes customer contact details

## Database Schema

### Email Logs Table

```sql
CREATE TABLE email_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  certificate_id UUID REFERENCES energieausweise(id) ON DELETE CASCADE,
  recipient_email TEXT NOT NULL,
  email_type TEXT NOT NULL CHECK (email_type IN ('customer_success', 'customer_failure', 'admin_success', 'admin_failure')),
  sent_at TIMESTAMP WITH TIME ZONE,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('sent', 'failed', 'pending')),
  error_message TEXT,
  resend_message_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Environment Variables

Add the following environment variables to your Supabase Edge Functions:

```bash
# Resend Email Service Configuration
RESEND_API_KEY=your-resend-api-key
ADMIN_EMAIL=<EMAIL>
FROM_EMAIL=<EMAIL>
SITE_URL=https://yourdomain.com
```

## Setup Instructions

### 1. Resend Account Setup

1. Create an account at [Resend](https://resend.com)
2. Verify your domain for email sending
3. Generate an API key
4. Add the API key to your environment variables

### 2. Environment Configuration

1. Update your `.env` file with the required variables
2. Deploy the environment variables to your Supabase project
3. Ensure the `FROM_EMAIL` uses a verified domain

### 3. Database Setup

The email logs table is automatically created when you run the provided SQL migration. The table includes:
- Row Level Security (RLS) policies
- Indexes for performance
- Automatic timestamp updates

## Integration Points

### Stripe Webhook Integration

The email service is integrated into the Stripe webhook handler (`supabase/functions/stripe-webhook/index.ts`):

- **Payment Success**: Triggers customer and admin success emails
- **Payment Failure**: Triggers customer and admin failure emails
- **Session Expiry**: Triggers failure notification emails

### Email Service Usage

```typescript
import { EmailService } from "../_shared/email-service.ts";

// Initialize the service
const emailService = new EmailService();

// Send payment success notifications
const results = await emailService.sendPaymentSuccessNotifications(certificateId);

// Send payment failure notifications
const results = await emailService.sendPaymentFailureNotifications(certificateId);

// Send individual notifications
const success = await emailService.sendCustomerSuccessNotification(certificateId);
```

## Testing

### Test Email Function

Use the test email function to verify email functionality:

```bash
# Test customer success email
curl -X POST https://your-project.supabase.co/functions/v1/test-email \
  -H "Content-Type: application/json" \
  -d '{
    "certificate_id": "your-certificate-id",
    "email_type": "customer_success"
  }'
```

### Available Test Types

- `customer_success`: Test customer success notification
- `customer_failure`: Test customer failure notification
- `admin_success`: Test admin success notification
- `admin_failure`: Test admin failure notification

## Admin Dashboard

### Email Logs View

The admin dashboard includes an email logs view (`src/components/admin/EmailLogsView.tsx`) that provides:

- **Email Log Listing**: View all sent emails with status
- **Filtering**: Filter by email type and status
- **Statistics**: Summary of email delivery statistics
- **Error Details**: View error messages for failed emails

### Accessing Email Logs

Email logs are accessible to:
- **Admins**: Can view all email logs
- **Users**: Can view logs for their own certificates
- **Service Role**: Can create and update email logs

## Error Handling

The email system includes comprehensive error handling:

1. **Service Initialization**: Gracefully handles missing environment variables
2. **Email Sending**: Logs failures without breaking the payment workflow
3. **Database Logging**: Continues operation even if logging fails
4. **Retry Logic**: Failed emails can be manually retried through the admin interface

## Monitoring and Maintenance

### Email Delivery Monitoring

Monitor email delivery through:
- **Database Logs**: Check the `email_logs` table for delivery status
- **Resend Dashboard**: Monitor delivery rates and bounces
- **Admin Dashboard**: View email statistics and failed deliveries

### Common Issues

1. **Domain Verification**: Ensure your sending domain is verified in Resend
2. **API Key**: Verify the Resend API key is correctly configured
3. **Rate Limits**: Monitor Resend rate limits for high-volume usage
4. **Spam Filters**: Ensure emails aren't being marked as spam

## Security Considerations

- **Environment Variables**: Store sensitive keys securely
- **Row Level Security**: Email logs are protected by RLS policies
- **Email Content**: Avoid including sensitive data in email content
- **Access Control**: Limit admin access to email logs

## Future Enhancements

Potential improvements to consider:

1. **Email Templates**: Move to external template system for easier management
2. **Retry Logic**: Implement automatic retry for failed emails
3. **Email Preferences**: Allow users to configure email preferences
4. **Analytics**: Add detailed email analytics and reporting
5. **Internationalization**: Support multiple languages for email content
