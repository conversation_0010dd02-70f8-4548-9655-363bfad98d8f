-- Create a function to get comprehensive payment analytics
-- This function calculates various payment metrics for the admin dashboard

CREATE OR REPLACE FUNCTION get_payment_analytics()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  total_certificates INTEGER;
  paid_certificates INTEGER;
  failed_certificates INTEGER;
  expired_certificates INTEGER;
  disputed_certificates INTEGER;
  unpaid_certificates INTEGER;
  total_revenue_cents BIGINT;
  conversion_rate NUMERIC;
  result JSON;
BEGIN
  -- Get total certificates count
  SELECT COUNT(*) INTO total_certificates
  FROM energieausweise;
  
  -- Get paid certificates count
  SELECT COUNT(*) INTO paid_certificates
  FROM energieausweise
  WHERE payment_status = 'paid';
  
  -- Get failed certificates count
  SELECT COUNT(*) INTO failed_certificates
  FROM energieausweise
  WHERE payment_status = 'failed';
  
  -- Get expired certificates count
  SELECT COUNT(*) INTO expired_certificates
  FROM energieausweise
  WHERE payment_status = 'expired';
  
  -- Get disputed certificates count
  SELECT COUNT(*) INTO disputed_certificates
  FROM energieausweise
  WHERE payment_status = 'disputed';
  
  -- Get unpaid certificates count
  SELECT COUNT(*) INTO unpaid_certificates
  FROM energieausweise
  WHERE payment_status = 'unpaid' OR payment_status IS NULL;
  
  -- Calculate total revenue (assuming €49.00 per certificate)
  total_revenue_cents := paid_certificates * 4900;
  
  -- Calculate conversion rate (paid / total with payment attempts)
  IF total_certificates > 0 THEN
    conversion_rate := ROUND((paid_certificates::NUMERIC / total_certificates::NUMERIC) * 100, 2);
  ELSE
    conversion_rate := 0;
  END IF;
  
  -- Build result JSON
  result := json_build_object(
    'total_certificates', total_certificates,
    'paid_certificates', paid_certificates,
    'failed_certificates', failed_certificates,
    'expired_certificates', expired_certificates,
    'disputed_certificates', disputed_certificates,
    'unpaid_certificates', unpaid_certificates,
    'total_revenue_cents', total_revenue_cents,
    'conversion_rate', conversion_rate
  );
  
  RETURN result;
END;
$$;

-- Grant execute permission to authenticated users (admin check will be done in RLS)
GRANT EXECUTE ON FUNCTION get_payment_analytics() TO authenticated;

-- Add comment explaining the function
COMMENT ON FUNCTION get_payment_analytics() IS 
'Returns comprehensive payment analytics including certificate counts by status, revenue, and conversion rates. Used by admin dashboard for monitoring payment performance.';
