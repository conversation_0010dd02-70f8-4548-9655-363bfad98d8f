# Stripe Products and Prices Setup Guide

This guide explains how to set up Stripe Products and Prices for the energy certificate pricing system.

## Overview

The pricing system uses a hybrid approach:
- **Supabase**: Stores pricing configuration and display information
- **Stripe**: Manages products, prices, and checkout sessions

## Step 1: Create Stripe Products

Create three products in your Stripe Dashboard for each certificate type:

### 1. Wohngebäude-Verbrauchsausweis (WG/V)
```
Name: Wohngebäude-Verbrauchsausweis
Description: Energieausweis für Wohngebäude basierend auf dem tatsächlichen Energieverbrauch
```

### 2. Wohngebäude-Bedarfsausweis (WG/B)
```
Name: Wohngebäude-Bedarfsausweis
Description: Energieausweis für Wohngebäude basierend auf dem berechneten Energiebedarf
```

### 3. Nicht-Wohngebäude-Verbrauchsausweis (NWG/V)
```
Name: Nicht-Wohngebäude-Verbrauchsausweis
Description: Energieausweis für Nicht-Wohngebäude basierend auf dem tatsächlichen Energieverbrauch
```

## Step 2: Create Stripe Prices

For each product, create a price:

```
Currency: EUR
Amount: €49.00 (or your desired price)
Billing: One-time
```

## Step 3: Update Database with Stripe IDs

After creating the products and prices, update the `certificate_pricing` table with the Stripe IDs:

```sql
-- Update WG/V pricing
UPDATE certificate_pricing 
SET 
  stripe_product_id = 'prod_XXXXXXXXXX',
  stripe_price_id = 'price_XXXXXXXXXX'
WHERE certificate_type = 'WG/V' AND is_active = true;

-- Update WG/B pricing
UPDATE certificate_pricing 
SET 
  stripe_product_id = 'prod_YYYYYYYYYY',
  stripe_price_id = 'price_YYYYYYYYYY'
WHERE certificate_type = 'WG/B' AND is_active = true;

-- Update NWG/V pricing
UPDATE certificate_pricing 
SET 
  stripe_product_id = 'prod_ZZZZZZZZZZ',
  stripe_price_id = 'price_ZZZZZZZZZZ'
WHERE certificate_type = 'NWG/V' AND is_active = true;
```

## Step 4: Alternative - Use Admin Interface

You can also update the Stripe IDs using the admin interface:

1. Go to `/admin` in your application
2. Navigate to the "Preisverwaltung" section
3. Click "Bearbeiten" for each certificate type
4. Enter the Stripe Product ID and Price ID
5. Click "Speichern"

## Step 5: Verify Integration

Test the integration by:

1. **Frontend Display**: Check that prices are displayed correctly on the homepage and summary page
2. **Checkout Process**: Create a test certificate and verify the checkout process uses the correct Stripe price
3. **Admin Interface**: Verify that the admin interface shows the correct pricing information

## Benefits of This Approach

### 1. **Flexibility**
- Admins can update prices without code deployments
- Support for different pricing strategies (discounts, regional pricing, etc.)

### 2. **Stripe Integration**
- Better reporting and analytics in Stripe Dashboard
- Consistent webhook handling
- Support for Stripe features like promotion codes

### 3. **Maintainability**
- Centralized pricing logic in the `PricingService`
- Type-safe operations with TypeScript
- Clear separation of concerns

## Troubleshooting

### Issue: Prices not updating on frontend
**Solution**: Check that the pricing query is being invalidated after updates. The admin interface should automatically invalidate relevant queries.

### Issue: Stripe checkout fails
**Solution**: Verify that the Stripe Price IDs are correct and that the prices are active in your Stripe Dashboard.

### Issue: Database migration fails
**Solution**: Ensure that the `is_admin_user()` function exists in your Supabase project. If not, you may need to create it or modify the RLS policies.

## Future Enhancements

The pricing system is designed to support future features:

- **Regional Pricing**: Different prices for different regions
- **Discount Codes**: Integration with Stripe promotion codes
- **Bulk Pricing**: Discounts for multiple certificates
- **Subscription Models**: Monthly/yearly pricing options
- **Currency Support**: Multi-currency pricing

## Security Considerations

- **RLS Policies**: Only admin users can modify pricing
- **Input Validation**: Price updates are validated on both client and server
- **Audit Trail**: All pricing changes are tracked with timestamps
- **Stripe Webhook Verification**: All webhook events are properly verified

## Monitoring

Monitor the pricing system through:

- **Supabase Dashboard**: Database queries and performance
- **Stripe Dashboard**: Payment analytics and success rates
- **Application Logs**: Pricing service operations and errors
- **Admin Interface**: Real-time pricing status and updates
