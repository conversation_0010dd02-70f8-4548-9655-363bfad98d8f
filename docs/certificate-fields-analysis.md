# Energy Certificate Fields Analysis

This document analyzes the fields from the Schnittstellenbeschreibung.md document to identify which fields are common across all certificate types and which are unique to specific types.

## Certificate Types

The document identifies three main types of energy certificates:
- **WG/V**: Wohngebäude/Verbrauchsausweis (Residential Building/Consumption Certificate)
- **WG/B**: Wohngebäude/Bedarfsausweis (Residential Building/Demand Certificate)
- **NWG/V**: Nichtwohngebäude/Verbrauchsausweis (Non-Residential Building/Consumption Certificate)

## Common Fields Across All Certificate Types

These fields are required for all three certificate types (WG/V, WG/B, and NWG/V):

### Basic Object Information
- ID
- Straße
- Hausnr
- PLZ
- Ort

### Customer Information
- Kunden_Anrede
- Kunden_Vorname
- Kunden_Nachname
- Kunden_Straße
- Kunden_Hausnr
- Kunden_PLZ
- Kunden_Ort
- Kunden_email
- Kunden_telefon

### Basic Building Information
- DIBt-Registriernummer
- gebaeudeteilAuto
- BedarfVerbrauch
- Anlass
- Datenerhebung
- nichtWohnGeb
- isGebaeudehuelle
- Baujahr
- Modernisierung
- Wohnfläche
- Klimatisiert
- ergaenzendeErlaeuterungen
- baujahrHzErz

### Building Characteristics
- Originaldämmstandard
- Fensterlüftung
- Schachtlüftung
- L_Mit_WRG
- L_Ohne_WRG

### Solar and Heat Pump Information
- TW_Solar

## Fields Specific to Residential Buildings (WG/V and WG/B)

These fields are common to both residential certificate types but not used for non-residential buildings:

- Wohneinheiten
- kuehlWfl

## Fields Specific to Residential Consumption Certificates (WG/V)

These fields are unique to WG/V:

- Keller_beheizt
- bjFensterAustausch
- HZ_Solar
- TW_WP
- HZ_WP
- WSchVo77_erfuellt

## Fields Specific to Residential Demand Certificates (WG/B)

These fields are unique to WG/B and involve detailed technical specifications:

- Raumhöhe
- Volumen
- Geschosse
- anbauSituation
- Detailed building component information (Boden, Dach, Wand, Fenster)
- Heating system details (Hzg_*)
- Drinking water system details (TW_*)
- Ventilation system details (Luft_*)

## Fields Specific to Non-Residential Consumption Certificates (NWG/V)

These fields are unique to NWG/V:

- Nutzung1_ID
- Nutzung1_Flaeche
- Energy consumption details (ETr1_* through ETr3_*)
- Additional usage specifications (ETr1_ZusatzHz, ETr1_Lueften, ETr1_Licht, ETr1_Kuehlen, ETr1_Sonst)

## Fields for Insulation Information

The following insulation-related fields exist across different certificate types:
- Boden1_Dämmung (all types)
- Dach1_Dämmung (all types)
- Wand1_Dämmung (all types)

## Fields for Energy Consumption (Verbrauchsausweis)

These fields are specific to consumption certificates (WG/V and NWG/V):
- ETr1_Kategorie
- ETr1_Heizung
- ETr1_TWW
- ETr1_PrimFaktor
- ETr1_Anteil_erneuerbar
- ETr1_Anteil_KWK
- ETr1_isFw
- ETr1_gebaeudeNahErzeugt
- ETr1_Name
- ETr1_Jahr1_von
- ETr1_Jahr1_bis
- ETr1_Jahr1_Menge
- ETr1_Jahr1_Menge_TWW
- ETr1_Jahr1_Leerstand
- (Similar fields for Jahr2 and Jahr3)

## Reusable Component Recommendations

Based on this analysis, the following reusable components are recommended:

1. **BasicObjectInfoForm**: For ID, address, and basic object information
2. **CustomerInfoForm**: For all customer-related fields
3. **BasicBuildingInfoForm**: For common building characteristics
4. **InsulationInfoForm**: For insulation-related fields
5. **SolarHeatPumpForm**: For solar and heat pump information
6. **EnergyConsumptionForm**: For consumption data (for V-type certificates)
7. **BuildingComponentsForm**: For detailed building components (for B-type certificates)
8. **HeatingSystemForm**: For heating system details (for B-type certificates)
9. **WaterSystemForm**: For drinking water system details (for B-type certificates)
10. **VentilationSystemForm**: For ventilation system details (for B-type certificates)
11. **NonResidentialUsageForm**: For non-residential specific fields (for NWG/V)

## Form Structure by Certificate Type

### WG/V (Residential Consumption Certificate)
1. BasicObjectInfoForm
2. CustomerInfoForm
3. BasicBuildingInfoForm
4. InsulationInfoForm
5. SolarHeatPumpForm
6. EnergyConsumptionForm
7. ResidentialSpecificForm (WG/V specific fields)

### WG/B (Residential Demand Certificate)
1. BasicObjectInfoForm
2. CustomerInfoForm
3. BasicBuildingInfoForm
4. InsulationInfoForm
5. SolarHeatPumpForm
6. BuildingComponentsForm
7. HeatingSystemForm
8. WaterSystemForm
9. VentilationSystemForm

### NWG/V (Non-Residential Consumption Certificate)
1. BasicObjectInfoForm
2. CustomerInfoForm
3. BasicBuildingInfoForm
4. InsulationInfoForm
5. SolarHeatPumpForm
6. EnergyConsumptionForm
7. NonResidentialUsageForm

This structure allows for maximum code reuse while accommodating the specific requirements of each certificate type.