# TanStack Query in der Verbrauchsausweis-App

## Was ist TanStack Query?

TanStack Query (früher bekannt als React Query) ist eine leistungsstarke Bibliothek für das Datenmanagement in React-Anwendungen. Sie bietet eine elegante Lösung für das Abrufen, Caching, Synchronisieren und Aktualisieren von Serverzustandsdaten in React-Anwendungen.

### Kernfunktionen von TanStack Query

1. **Datenabfrage und -caching**: Automatisches Caching von Serverantworten und intelligente Wiederverwendung dieser Daten.
2. **Automatische Aktualisierung**: Hintergrundaktualisierung veralteter Daten und Anzeige der neuesten Daten.
3. **Ladezustände**: Einfache Verwaltung von Ladezuständen (loading, error, success).
4. **Pagination und unendliches Scrollen**: Integrierte Unterstützung für Pagination und unendliches Scrollen.
5. **Daten-Mutation**: Einfache API für das Aktualisieren von Serverdaten.
6. **Devtools**: Integrierte Entwicklertools zur Visualisierung des Zustands der Abfragen.

## Grundprinzipien von TanStack Query

### Queries (Abfragen)

Queries werden verwendet, um Daten vom Server abzurufen. Sie werden mit dem `useQuery`-Hook definiert:

```tsx
const { data, isLoading, isError, error } = useQuery({
  queryKey: ['uniqueKey'],
  queryFn: async () => {
    // Funktion zum Abrufen der Daten
    const response = await fetch('/api/data');
    return response.json();
  },
});
```

- **queryKey**: Ein eindeutiger Schlüssel, der die Abfrage identifiziert (kann ein Array sein)
- **queryFn**: Eine asynchrone Funktion, die die Daten abruft
- **Rückgabewerte**: Enthält Daten und Metadaten wie Ladezustand, Fehler usw.

### Mutations (Mutationen)

Mutations werden verwendet, um Daten auf dem Server zu aktualisieren. Sie werden mit dem `useMutation`-Hook definiert:

```tsx
const mutation = useMutation({
  mutationFn: async (newData) => {
    // Funktion zum Aktualisieren der Daten
    const response = await fetch('/api/data', {
      method: 'POST',
      body: JSON.stringify(newData),
    });
    return response.json();
  },
  onSuccess: () => {
    // Wird aufgerufen, wenn die Mutation erfolgreich ist
    queryClient.invalidateQueries({ queryKey: ['uniqueKey'] });
  },
});

// Verwendung:
mutation.mutate(newData);
```

### Query Client

Der QueryClient verwaltet den Cache und den Zustand aller Abfragen und Mutationen:

```tsx
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

const queryClient = new QueryClient();

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      {/* Anwendungskomponenten */}
    </QueryClientProvider>
  );
}
```

### Invalidierung des Cache

Nach einer Mutation müssen oft Abfragen aktualisiert werden. Dies geschieht durch Invalidierung des Cache:

```tsx
queryClient.invalidateQueries({ queryKey: ['uniqueKey'] });
```

## Implementierung in der Verbrauchsausweis-App

In der Verbrauchsausweis-App wird TanStack Query hauptsächlich für folgende Zwecke verwendet:

### 1. Abrufen von Benutzerdaten und Formularinhalten

Die App verwendet TanStack Query, um Daten aus der Supabase-Datenbank abzurufen. Ein Beispiel dafür ist die Abfrage von Objektdaten in der `ObjektdatenPage.tsx`:

```tsx
const { data: existingData, isError, error } = useQuery({
  queryKey: ['energieausweise', 'objektdaten'],
  queryFn: async () => {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('Nicht eingeloggt');
    
    const { data, error } = await supabase
      .from('energieausweise')
      .select('objektdaten')
      .eq('user_id', user.user.id)
      .single();
    
    if (error && error.code !== 'PGRST116') { 
      throw error;
    }
    
    return data; 
  },
  retry: false,
});
```

Diese Abfrage:
- Verwendet den Schlüssel `['energieausweise', 'objektdaten']` zur Identifizierung
- Ruft die Objektdaten des aktuell eingeloggten Benutzers ab
- Behandelt den Fall, dass keine Daten gefunden wurden (PGRST116-Fehlercode)
- Deaktiviert automatische Wiederholungsversuche mit `retry: false`

### 2. Speichern von Formulardaten

Für das Speichern von Daten wird `useMutation` verwendet, wie in diesem Beispiel aus der `ObjektdatenPage.tsx`:

```tsx
const saveMutation = useMutation({
  mutationFn: async (formData: ObjektdatenFormValues) => {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated for saving.');

    const { data: result, error } = await supabase
      .from('energieausweise')
      .upsert({
        user_id: user.user.id,
        objektdaten: formData,
        updated_at: new Date().toISOString(),
      }, {
        onConflict: 'user_id',
      })
      .select()
      .single();

    if (error) throw error;
    return result;
  },
  onSuccess: () => {
    queryClient.invalidateQueries({ queryKey: ['energieausweise', 'objektdaten'] });
    queryClient.invalidateQueries({ queryKey: ['energieausweise'] });
    navigate({ to: '/erfassen/objektdaten' });
  },
  onError: (error) => {
    setSubmitError(`Fehler beim Speichern: ${error.message}`);
  },
});
```

Diese Mutation:
- Definiert eine Funktion zum Speichern der Formulardaten in Supabase
- Verwendet `upsert` für das Einfügen oder Aktualisieren von Daten
- Invalidiert bei Erfolg die relevanten Abfragen, um den Cache zu aktualisieren
- Navigiert den Benutzer zur Übersichtsseite
- Behandelt Fehler und zeigt sie dem Benutzer an

### 3. Globale Konfiguration

Die App konfiguriert den QueryClient in der Hauptanwendungsdatei, um ihn für alle Komponenten verfügbar zu machen. Dies ermöglicht eine konsistente Datenverwaltung in der gesamten Anwendung.

### 4. Vorteile der Implementierung

Die Verwendung von TanStack Query in der Verbrauchsausweis-App bietet mehrere Vorteile:

- **Verbesserte Benutzererfahrung**: Automatisches Laden und Aktualisieren von Daten ohne zusätzlichen Code
- **Reduzierter Boilerplate-Code**: Weniger Code für Ladezustände, Fehlerbehandlung und Caching
- **Konsistente Datenverwaltung**: Einheitlicher Ansatz für alle Datenoperationen
- **Optimierte Serveranfragen**: Reduzierung unnötiger Anfragen durch intelligentes Caching
- **Einfache Fehlerbehandlung**: Integrierte Mechanismen zur Behandlung von Netzwerk- und Serverfehlern

## Best Practices für TanStack Query in diesem Projekt

1. **Strukturierte Query Keys**: Verwenden Sie strukturierte Query Keys, um die Abfragen zu organisieren und die Invalidierung zu erleichtern.
   ```tsx
   // Gut
   queryKey: ['energieausweise', 'objektdaten', userId]
   
   // Vermeiden
   queryKey: ['objektdatenFürBenutzer' + userId]
   ```

2. **Fehlerbehandlung**: Implementieren Sie immer eine angemessene Fehlerbehandlung für Abfragen und Mutationen.

3. **Optimistische Updates**: Für eine bessere Benutzererfahrung können optimistische Updates verwendet werden, bei denen die UI aktualisiert wird, bevor die Serverantwort eintrifft.

4. **Prefetching**: Daten können im Voraus abgerufen werden, um die Benutzererfahrung zu verbessern.

5. **Abfragen deaktivieren**: Abfragen können bedingt deaktiviert werden, wenn sie nicht benötigt werden.
   ```tsx
   const { data } = useQuery({
     queryKey: ['data'],
     queryFn: fetchData,
     enabled: isReady,
   });
   ```

## Fazit

TanStack Query ist ein leistungsstarkes Werkzeug für das Datenmanagement in der Verbrauchsausweis-App. Es vereinfacht die Kommunikation mit dem Supabase-Backend erheblich und verbessert die Benutzererfahrung durch intelligentes Caching und automatische Aktualisierungen. Die Bibliothek reduziert den Boilerplate-Code und ermöglicht es den Entwicklern, sich auf die Geschäftslogik zu konzentrieren, anstatt sich mit der Datenverwaltung zu befassen.