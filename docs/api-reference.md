# API Reference - Stripe Webhook Enhancements

## Edge Functions API

### stripe-webhook

**Endpoint**: `POST /functions/v1/stripe-webhook`

**Purpose**: Processes Stripe webhook events for payment lifecycle management.

#### Headers
```
Content-Type: application/json
Stripe-Signature: t=timestamp,v1=signature
```

#### Supported Webhook Events

| Event Type | Status Update | Email Notification | Description |
|------------|---------------|-------------------|-------------|
| `checkout.session.completed` | `paid` | Success | Payment completed successfully |
| `checkout.session.expired` | `expired` | Failure | Session expired without payment |
| `checkout.session.async_payment_failed` | `failed` | Failure | Async payment processing failed |
| `checkout.session.async_payment_succeeded` | `paid` | Success | Async payment completed |
| `payment_intent.succeeded` | - | - | Payment intent succeeded (logged only) |
| `payment_intent.payment_failed` | `failed` | Failure | Payment intent failed |
| `charge.failed` | `failed` | Failure | Charge processing failed |
| `charge.dispute.created` | `disputed` | Failure | Chargeback/dispute created |
| `invoice.payment_failed` | `failed` | Failure | Invoice payment failed |
| `payment_method.automatically_updated` | - | - | Payment method updated (logged only) |

#### Response Format

**Success (200)**:
```json
{
  "ok": true,
  "event_id": "evt_1234567890",
  "event_type": "checkout.session.completed",
  "processing_status": "handled"
}
```

**Error (400)**:
```json
{
  "error": "Invalid signature",
  "ok": false
}
```

#### Certificate ID Extraction Methods

The webhook handler extracts certificate IDs using multiple fallback methods:

1. **client_reference_id** (Checkout Sessions)
2. **payment_intent_metadata** (Payment Intents)
3. **charge_metadata** (Charges)
4. **invoice_metadata** (Invoices)

### log-payment-cancellation

**Endpoint**: `POST /functions/v1/log-payment-cancellation`

**Purpose**: Logs payment cancellation events for analytics and monitoring.

#### Request Body

```typescript
interface PaymentCancellationLog {
  sessionId?: string;        // Stripe checkout session ID
  certificateId?: string;    // Energy certificate ID (optional if sessionId provided)
  reason: string;           // Cancellation reason
  userAgent?: string;       // Browser user agent
  timestamp: string;        // ISO 8601 timestamp
}
```

#### Example Request

```javascript
const response = await fetch('/functions/v1/log-payment-cancellation', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${supabaseAnonKey}`
  },
  body: JSON.stringify({
    sessionId: 'cs_test_1234567890',
    reason: 'user_cancelled',
    userAgent: navigator.userAgent,
    timestamp: new Date().toISOString()
  })
});
```

#### Response Format

**Success (200)**:
```json
{
  "success": true,
  "message": "Payment cancellation logged successfully",
  "certificateId": "uuid-here"
}
```

**Error (500)**:
```json
{
  "success": false,
  "error": "Error message details"
}
```

#### Cancellation Reasons

| Reason | Description | Trigger |
|--------|-------------|---------|
| `user_cancelled` | User clicked back/cancel button | Manual user action |
| `session_timeout` | Session expired (30 minutes) | Automatic timeout |
| `technical_error` | System or network error | Error detection |
| `payment_method_error` | Payment method issues | Stripe error response |
| `card_declined` | Card declined by issuer | Stripe decline |
| `insufficient_funds` | Insufficient account balance | Bank decline |

### check-abandoned-sessions

**Endpoint**: `POST /functions/v1/check-abandoned-sessions`

**Purpose**: Proactively checks and updates abandoned payment sessions.

#### Request Body

No request body required. Function operates on database queries.

#### Response Format

```json
{
  "success": true,
  "message": "Abandoned session check completed",
  "results": {
    "checked": 25,
    "abandoned": 8,
    "stillActive": 12,
    "expired": 3,
    "errors": 2
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### Results Interpretation

| Field | Description |
|-------|-------------|
| `checked` | Total sessions examined |
| `abandoned` | Sessions marked as abandoned |
| `stillActive` | Sessions still in progress |
| `expired` | Sessions that expired |
| `errors` | Sessions with processing errors |

#### Scheduling Recommendations

```bash
# Cron job example (every 15 minutes)
*/15 * * * * curl -X POST https://your-project.supabase.co/functions/v1/check-abandoned-sessions

# Or using Supabase scheduled functions
```

## Database Functions API

### get_payment_analytics()

**Purpose**: Returns comprehensive payment analytics for admin dashboard.

#### SQL Usage

```sql
SELECT get_payment_analytics();
```

#### Response Structure

```json
{
  "total_certificates": 150,
  "paid_certificates": 120,
  "failed_certificates": 15,
  "expired_certificates": 10,
  "disputed_certificates": 2,
  "unpaid_certificates": 3,
  "total_revenue_cents": 588000,
  "conversion_rate": 80.00
}
```

#### Field Definitions

| Field | Type | Description |
|-------|------|-------------|
| `total_certificates` | INTEGER | Total number of certificates created |
| `paid_certificates` | INTEGER | Successfully paid certificates |
| `failed_certificates` | INTEGER | Failed payment certificates |
| `expired_certificates` | INTEGER | Expired session certificates |
| `disputed_certificates` | INTEGER | Disputed/chargeback certificates |
| `unpaid_certificates` | INTEGER | Unpaid certificates |
| `total_revenue_cents` | BIGINT | Total revenue in cents (paid × 4900) |
| `conversion_rate` | NUMERIC | Percentage of successful payments |

#### JavaScript Usage

```javascript
const { data, error } = await supabase.rpc('get_payment_analytics');
if (error) throw error;

console.log(`Conversion rate: ${data.conversion_rate}%`);
console.log(`Revenue: €${data.total_revenue_cents / 100}`);
```

## Database Tables API

### payment_attempts

**Purpose**: Tracks complete payment attempt lifecycle.

#### Table Structure

```sql
CREATE TABLE payment_attempts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  certificate_id UUID REFERENCES energieausweise(id),
  stripe_session_id TEXT,
  stripe_payment_intent_id TEXT,
  attempt_status TEXT DEFAULT 'initiated',
  payment_method TEXT,
  amount_cents INTEGER,
  currency TEXT DEFAULT 'eur',
  abandonment_reason TEXT,
  failure_reason TEXT,
  user_agent TEXT,
  ip_address INET,
  session_duration_seconds INTEGER,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### Query Examples

**Recent Payment Attempts**:
```sql
SELECT 
  pa.attempt_status,
  pa.session_duration_seconds,
  pa.abandonment_reason,
  e.certificate_type,
  e.payment_status
FROM payment_attempts pa
JOIN energieausweise e ON pa.certificate_id = e.id
WHERE pa.created_at > NOW() - INTERVAL '24 hours'
ORDER BY pa.created_at DESC;
```

**Abandonment Analysis**:
```sql
SELECT 
  abandonment_reason,
  COUNT(*) as count,
  AVG(session_duration_seconds) as avg_duration
FROM payment_attempts 
WHERE attempt_status = 'abandoned'
AND created_at > NOW() - INTERVAL '7 days'
GROUP BY abandonment_reason
ORDER BY count DESC;
```

**Conversion Funnel**:
```sql
SELECT 
  attempt_status,
  COUNT(*) as count,
  ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as percentage
FROM payment_attempts
WHERE created_at > NOW() - INTERVAL '30 days'
GROUP BY attempt_status
ORDER BY count DESC;
```

### stripe_webhook_events

**Purpose**: Comprehensive logging of all Stripe webhook events.

#### Query Examples

**Event Processing Summary**:
```sql
SELECT 
  event_type,
  processing_status,
  COUNT(*) as count,
  MAX(created_at) as latest_event
FROM stripe_webhook_events
WHERE created_at > NOW() - INTERVAL '7 days'
GROUP BY event_type, processing_status
ORDER BY latest_event DESC;
```

**Failed Event Analysis**:
```sql
SELECT 
  event_type,
  error_message,
  COUNT(*) as error_count
FROM stripe_webhook_events
WHERE processing_status = 'error'
AND created_at > NOW() - INTERVAL '24 hours'
GROUP BY event_type, error_message
ORDER BY error_count DESC;
```

**Certificate Event Timeline**:
```sql
SELECT 
  swe.event_type,
  swe.processing_status,
  swe.event_created_at,
  e.payment_status
FROM stripe_webhook_events swe
JOIN energieausweise e ON swe.certificate_id = e.id
WHERE e.id = 'certificate-uuid-here'
ORDER BY swe.event_created_at ASC;
```

## Frontend Integration API

### React Query Hooks

#### Payment Analytics Hook

```typescript
const usePaymentAnalytics = () => {
  return useQuery({
    queryKey: ['paymentAnalytics'],
    queryFn: async () => {
      const { data, error } = await supabase.rpc('get_payment_analytics');
      if (error) throw error;
      return data;
    },
    refetchInterval: 30000, // Auto-refresh every 30 seconds
  });
};
```

#### Webhook Events Hook

```typescript
const useWebhookEvents = (days: number = 7) => {
  return useQuery({
    queryKey: ['webhookEvents', days],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('stripe_webhook_events')
        .select('event_type, processing_status, created_at')
        .gte('created_at', new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data;
    },
  });
};
```

#### Payment Attempts Hook

```typescript
const usePaymentAttempts = (certificateId?: string) => {
  return useQuery({
    queryKey: ['paymentAttempts', certificateId],
    queryFn: async () => {
      let query = supabase
        .from('payment_attempts')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (certificateId) {
        query = query.eq('certificate_id', certificateId);
      }
      
      const { data, error } = await query;
      if (error) throw error;
      return data;
    },
    enabled: !!certificateId,
  });
};
```

### Mutation Hooks

#### Log Cancellation Mutation

```typescript
const useLogCancellation = () => {
  return useMutation({
    mutationFn: async (cancellationData: PaymentCancellationLog) => {
      const { data, error } = await supabase.functions.invoke(
        'log-payment-cancellation',
        { body: cancellationData }
      );
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      console.log('Cancellation logged successfully');
    },
    onError: (error) => {
      console.error('Failed to log cancellation:', error);
    },
  });
};
```

#### Track Payment Attempt Mutation

```typescript
const useTrackPaymentAttempt = () => {
  return useMutation({
    mutationFn: async (attemptData: PaymentAttemptData) => {
      const { data, error } = await supabase
        .from('payment_attempts')
        .insert(attemptData)
        .select()
        .single();
      if (error) throw error;
      return data;
    },
  });
};
```

## Error Handling

### Standard Error Responses

All API endpoints follow consistent error response formats:

```typescript
interface ErrorResponse {
  success: false;
  error: string;
  code?: string;
  details?: any;
}
```

### Common Error Codes

| Code | Description | Resolution |
|------|-------------|------------|
| `INVALID_SIGNATURE` | Webhook signature verification failed | Check webhook secret |
| `MISSING_CERTIFICATE_ID` | Cannot extract certificate ID from event | Verify metadata setup |
| `DATABASE_ERROR` | Database operation failed | Check connection and permissions |
| `STRIPE_API_ERROR` | Stripe API call failed | Verify API keys and rate limits |
| `AUTHENTICATION_ERROR` | Invalid or missing authentication | Check JWT token |
| `PERMISSION_DENIED` | Insufficient permissions | Verify user role and RLS policies |

### Rate Limiting

| Endpoint | Rate Limit | Window |
|----------|------------|--------|
| `stripe-webhook` | 1000 req/min | Per webhook endpoint |
| `log-payment-cancellation` | 100 req/min | Per user |
| `check-abandoned-sessions` | 10 req/min | Global |

### Retry Policies

- **Webhook Events**: Stripe automatically retries failed webhooks with exponential backoff
- **Database Operations**: Implement client-side retry with exponential backoff for transient errors
- **API Calls**: Use circuit breaker pattern for external API calls

This API reference provides comprehensive documentation for integrating with the enhanced Stripe webhook system. For additional examples and use cases, refer to the implementation files in the codebase.
