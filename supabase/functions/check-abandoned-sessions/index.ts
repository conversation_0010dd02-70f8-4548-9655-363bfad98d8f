import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.49.1";
import Stripe from "https://esm.sh/stripe@14?target=denonext";

// CORS headers to allow cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS'
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }

  try {
    const supabaseUrl = Deno.env.get("SUPABASE_URL");
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");
    const stripeSecretKey = Deno.env.get("STRIPE_SECRET_KEY");

    if (!supabaseUrl || !supabaseServiceKey || !stripeSecretKey) {
      throw new Error("Required environment variables are missing");
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    const stripe = new Stripe(stripeSecretKey, {
      apiVersion: '2024-11-20'
    });

    console.log("Checking for abandoned payment sessions...");

    // Find sessions that were created but not completed within the last 2 hours
    const twoHoursAgo = new Date(Date.now() - 2 * 60 * 60 * 1000);
    const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);

    const { data: potentiallyAbandoned, error: queryError } = await supabase
      .from('energieausweise')
      .select('id, stripe_checkout_session_id, created_at, payment_status')
      .eq('payment_status', 'unpaid')
      .not('stripe_checkout_session_id', 'is', null)
      .gte('created_at', twoHoursAgo.toISOString())
      .lt('created_at', thirtyMinutesAgo.toISOString());

    if (queryError) {
      throw new Error(`Database query failed: ${queryError.message}`);
    }

    console.log(`Found ${potentiallyAbandoned?.length || 0} potentially abandoned sessions`);

    const results = {
      checked: 0,
      abandoned: 0,
      stillActive: 0,
      expired: 0,
      errors: 0
    };

    if (potentiallyAbandoned && potentiallyAbandoned.length > 0) {
      for (const certificate of potentiallyAbandoned) {
        results.checked++;
        
        try {
          // Check the actual Stripe session status
          const session = await stripe.checkout.sessions.retrieve(certificate.stripe_checkout_session_id);
          
          console.log(`Session ${session.id} status: ${session.status}, payment_status: ${session.payment_status}`);

          let newStatus = certificate.payment_status;
          let attemptStatus = 'initiated';
          let abandonmentReason = null;

          // Determine the actual status based on Stripe session
          if (session.status === 'expired') {
            newStatus = 'expired';
            attemptStatus = 'expired';
            abandonmentReason = 'session_timeout';
            results.expired++;
          } else if (session.status === 'complete' && session.payment_status === 'paid') {
            newStatus = 'paid';
            attemptStatus = 'succeeded';
            results.stillActive++; // Actually completed
          } else if (session.status === 'open') {
            // Session is still open, check if it's been too long
            const sessionAge = Date.now() - (session.created * 1000);
            if (sessionAge > 30 * 60 * 1000) { // 30 minutes
              attemptStatus = 'abandoned';
              abandonmentReason = 'session_timeout_likely';
              results.abandoned++;
            } else {
              results.stillActive++;
              continue; // Skip updating this one
            }
          } else {
            // Other statuses (incomplete, etc.)
            attemptStatus = 'abandoned';
            abandonmentReason = `stripe_status_${session.status}`;
            results.abandoned++;
          }

          // Update the certificate status if it changed
          if (newStatus !== certificate.payment_status) {
            const { error: updateError } = await supabase
              .from('energieausweise')
              .update({
                payment_status: newStatus,
                updated_at: new Date().toISOString()
              })
              .eq('id', certificate.id);

            if (updateError) {
              console.error(`Error updating certificate ${certificate.id}:`, updateError);
              results.errors++;
            } else {
              console.log(`Updated certificate ${certificate.id} status to ${newStatus}`);
            }
          }

          // Create or update payment attempt record
          const sessionDuration = Math.floor((Date.now() - (session.created * 1000)) / 1000);
          
          const { data: existingAttempt } = await supabase
            .from('payment_attempts')
            .select('id')
            .eq('certificate_id', certificate.id)
            .eq('stripe_session_id', certificate.stripe_checkout_session_id)
            .single();

          if (existingAttempt) {
            // Update existing attempt
            const { error: updateAttemptError } = await supabase
              .from('payment_attempts')
              .update({
                attempt_status: attemptStatus,
                abandonment_reason: abandonmentReason,
                session_duration_seconds: sessionDuration,
                completed_at: attemptStatus !== 'initiated' ? new Date().toISOString() : null,
                updated_at: new Date().toISOString()
              })
              .eq('id', existingAttempt.id);

            if (updateAttemptError) {
              console.error(`Error updating payment attempt:`, updateAttemptError);
            }
          } else {
            // Create new payment attempt record
            const { error: insertAttemptError } = await supabase
              .from('payment_attempts')
              .insert({
                certificate_id: certificate.id,
                stripe_session_id: certificate.stripe_checkout_session_id,
                attempt_status: attemptStatus,
                abandonment_reason: abandonmentReason,
                amount_cents: session.amount_total,
                currency: session.currency,
                session_duration_seconds: sessionDuration,
                completed_at: attemptStatus !== 'initiated' ? new Date().toISOString() : null
              });

            if (insertAttemptError) {
              console.error(`Error creating payment attempt:`, insertAttemptError);
            }
          }

        } catch (stripeError) {
          console.error(`Error checking Stripe session ${certificate.stripe_checkout_session_id}:`, stripeError);
          results.errors++;
        }
      }
    }

    console.log("Abandoned session check completed:", results);

    return new Response(JSON.stringify({
      success: true,
      message: 'Abandoned session check completed',
      results: results,
      timestamp: new Date().toISOString()
    }), {
      status: 200,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });

  } catch (error) {
    console.error(`Error checking abandoned sessions: ${error.message}`);
    
    return new Response(JSON.stringify({
      error: error.message,
      success: false
    }), {
      status: 500,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  }
});
