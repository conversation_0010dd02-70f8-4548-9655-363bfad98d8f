# Image Quality Validation Implementation

## Overview

This document describes the implementation of image quality validation for building image uploads (gebaeudebild) in the DirectoryBasedFileUpload component.

## Requirements

Based on the user requirements, images should be:
- **Size**: 6cm x 4cm at 60 pixels per cm
- **Minimum dimensions**: 360 x 240 pixels (6cm × 60px/cm × 4cm × 60px/cm)
- **Maximum dimensions**: 4000 x 3000 pixels (reasonable upper limit)
- **Supported formats**: JPG, PNG, WebP
- **Maximum file size**: 5 MB

## Implementation Details

### 1. Dimension Requirements Constants

```typescript
const IMAGE_DIMENSION_REQUIREMENTS = {
  minWidth: 360,   // 6cm * 60px/cm
  minHeight: 240,  // 4cm * 60px/cm
  maxWidth: 4000,  // Maximum reasonable width
  maxHeight: 3000  // Maximum reasonable height
};
```

### 2. Image Validation Function

The `validateImageDimensions` function:
- Only validates images for the 'gebaeudebild' field
- Only processes files with image MIME types
- Uses the HTML5 Image API to read dimensions
- Returns a Promise with validation results
- <PERSON><PERSON>ly manages object URLs to prevent memory leaks

### 3. Async Validation Integration

The validation is integrated into the existing file validation flow:
- `validateFiles` function is now async
- `handleFileChange` properly handles async validation
- File input is reset on validation failure
- Clear error messages are displayed to users

### 4. User Experience Enhancements

#### Information Panel
For building image uploads, users see helpful guidance:
- Minimum and maximum resolution requirements
- Supported file formats
- File size limits
- Clear explanation of the 60px/cm standard

#### Error Messages
Validation errors provide specific information:
- Current image dimensions
- Required dimension ranges
- Helpful tips for resolution

#### File Input Reset
When validation fails:
- File input is cleared
- User can immediately select a different file
- No partial upload states

## Error Message Examples

### Too Small Image
```
test-image.jpg: Bild zu klein (mindestens 360x240 Pixel erforderlich, aktuell: 300x200 Pixel)
```

### Too Large Image
```
large-image.jpg: Bild zu groß (maximal 4000x3000 Pixel erlaubt, aktuell: 5000x4000 Pixel)
```

### Unreadable Image
```
corrupt-image.jpg: Bilddatei konnte nicht gelesen werden
```

## Field-Specific Behavior

The validation only applies to the `gebaeudebild` field:
- Other fields (verbrauchsrechnung1, verbrauchsrechnung2, verbrauchsrechnung3) are unaffected
- PDF files and other non-image files skip dimension validation
- Maintains backward compatibility with existing functionality

## Technical Implementation

### Client-Side Validation
- Validation occurs immediately after file selection
- No server requests for invalid files
- Prevents unnecessary upload attempts
- Provides instant feedback to users

### Memory Management
- Object URLs are properly created and revoked
- No memory leaks from image loading
- Efficient handling of multiple file selections

### Error Handling
- Graceful handling of corrupt or unreadable images
- Clear error messages in German
- Maintains application stability

## Testing

### Automated Tests
- Unit tests for dimension validation logic
- Mock Image API for consistent testing
- Error message format validation
- File type handling verification

### Manual Testing
- Interactive HTML test page (`docs/test-image-validation.html`)
- Test cases for various image sizes
- Validation of error messages
- User experience verification

## Integration Points

### Component Props
No changes to the component's public API:
- Existing props remain unchanged
- Backward compatibility maintained
- Field-specific behavior is internal

### File Upload Flow
1. User selects file(s)
2. Basic validation (size, type, duplicates)
3. **NEW**: Image dimension validation (for gebaeudebild only)
4. Display validation results
5. Proceed with upload if valid

### Error Display
Enhanced error display includes:
- Existing validation errors
- New dimension validation errors
- Helpful tips for image resolution
- Clear visual distinction between error types

## Benefits

### User Experience
- Clear guidance on image requirements
- Immediate feedback on validation
- Helpful error messages in German
- No wasted time on invalid uploads

### System Performance
- Client-side validation reduces server load
- Prevents invalid file uploads
- Efficient memory usage
- Fast validation response

### Maintainability
- Clean separation of validation logic
- Easy to modify dimension requirements
- Comprehensive test coverage
- Well-documented implementation

## Future Enhancements

Potential improvements could include:
- Automatic image resizing suggestions
- Integration with image editing tools
- Batch validation for multiple images
- Progressive image quality analysis
- Advanced format support (AVIF, etc.)

## Configuration

The dimension requirements can be easily modified by updating the `IMAGE_DIMENSION_REQUIREMENTS` constant. This allows for future adjustments without changing the validation logic.

## Conclusion

The image validation implementation provides a robust, user-friendly solution for ensuring building images meet the specified quality requirements. The implementation maintains backward compatibility while adding essential validation functionality specifically for the gebaeudebild field.
