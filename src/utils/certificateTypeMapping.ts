/**
 * Utility functions for certificate type mapping
 * This file contains the logic for automatically setting BedarfVerbrauch and nichtWohnGeb
 * based on the certificate type selection.
 */

export type CertificateType = 'WG/V' | 'WG/B' | 'NWG/V';

export interface AutomaticValues {
  BedarfVerbrauch: 'V' | 'B';
  nichtWohnGeb: '0' | '1';
}

/**
 * Get automatic values based on certificate type
 * 
 * Certificate type mapping:
 * - WG/V: BedarfVerbrauch="V", nichtWohnGeb="0" (Residential Consumption Certificate)
 * - WG/B: BedarfVerbrauch="B", nichtWohnGeb="0" (Residential Demand Certificate)  
 * - NWG/V: BedarfVerbrauch="V", nichtWohnGeb="1" (Non-Residential Consumption Certificate)
 */
export const getAutomaticValues = (certificateType: CertificateType): AutomaticValues => {
  switch (certificateType) {
    case 'WG/V':
      return { BedarfVerbrauch: 'V', nichtWohnGeb: '0' };
    case 'WG/B':
      return { BedarfVerbrauch: 'B', nichtWohnGeb: '0' };
    case 'NWG/V':
      return { BedarfVerbrauch: 'V', nichtWohnGeb: '1' };
    default:
      // Default fallback
      return { BedarfVerbrauch: 'V', nichtWohnGeb: '0' };
  }
};

/**
 * Get human-readable labels for the automatic values
 */
export const getAutomaticValueLabels = (certificateType: CertificateType) => {
  const values = getAutomaticValues(certificateType);
  return {
    BedarfVerbrauch: values.BedarfVerbrauch === 'V' ? 'Verbrauch (V)' : 'Bedarf (B)',
    nichtWohnGeb: values.nichtWohnGeb === '0' ? 'Wohngebäude' : 'Nichtwohngebäude'
  };
};

/**
 * Validate that the certificate type mapping is correct
 */
export const validateCertificateTypeMapping = () => {
  const testCases: Array<{
    certificateType: CertificateType;
    expectedBedarfVerbrauch: 'V' | 'B';
    expectedNichtWohnGeb: '0' | '1';
  }> = [
    { certificateType: 'WG/V', expectedBedarfVerbrauch: 'V', expectedNichtWohnGeb: '0' },
    { certificateType: 'WG/B', expectedBedarfVerbrauch: 'B', expectedNichtWohnGeb: '0' },
    { certificateType: 'NWG/V', expectedBedarfVerbrauch: 'V', expectedNichtWohnGeb: '1' },
  ];

  const results = testCases.map(testCase => {
    const actual = getAutomaticValues(testCase.certificateType);
    const isValid = 
      actual.BedarfVerbrauch === testCase.expectedBedarfVerbrauch &&
      actual.nichtWohnGeb === testCase.expectedNichtWohnGeb;
    
    return {
      certificateType: testCase.certificateType,
      expected: {
        BedarfVerbrauch: testCase.expectedBedarfVerbrauch,
        nichtWohnGeb: testCase.expectedNichtWohnGeb
      },
      actual,
      isValid
    };
  });

  return {
    allValid: results.every(r => r.isValid),
    results
  };
};
