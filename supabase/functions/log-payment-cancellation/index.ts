import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.49.1";

// CORS headers to allow cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS'
};

interface PaymentCancellationLog {
  sessionId?: string;
  certificateId?: string;
  reason: string;
  userAgent?: string;
  timestamp: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }

  try {
    const supabaseUrl = Deno.env.get("SUPABASE_URL");
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");

    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error("Supabase environment variables are required");
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Parse the request body
    const { sessionId, certificateId, reason, userAgent }: PaymentCancellationLog = await req.json();

    console.log("Logging payment cancellation:", {
      sessionId,
      certificateId,
      reason,
      userAgent
    });

    // Try to find the certificate ID if not provided
    let finalCertificateId = certificateId;
    if (!finalCertificateId && sessionId) {
      // Look up certificate by stripe session ID
      const { data: certificate } = await supabase
        .from('energieausweise')
        .select('id')
        .eq('stripe_checkout_session_id', sessionId)
        .single();
      
      if (certificate) {
        finalCertificateId = certificate.id;
      }
    }

    // Log the cancellation in payment_attempts table
    if (finalCertificateId) {
      // First, try to find an existing payment attempt for this session
      const { data: existingAttempt } = await supabase
        .from('payment_attempts')
        .select('id, created_at')
        .eq('certificate_id', finalCertificateId)
        .eq('stripe_session_id', sessionId)
        .single();

      if (existingAttempt) {
        // Update existing attempt
        const sessionDuration = existingAttempt.created_at 
          ? Math.floor((new Date().getTime() - new Date(existingAttempt.created_at).getTime()) / 1000)
          : null;

        const { error: updateError } = await supabase
          .from('payment_attempts')
          .update({
            attempt_status: 'abandoned',
            abandonment_reason: reason,
            session_duration_seconds: sessionDuration,
            completed_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('id', existingAttempt.id);

        if (updateError) {
          console.error('Error updating payment attempt:', updateError);
        } else {
          console.log(`Updated payment attempt ${existingAttempt.id} as abandoned`);
        }
      } else {
        // Create new payment attempt record
        const { error: insertError } = await supabase
          .from('payment_attempts')
          .insert({
            certificate_id: finalCertificateId,
            stripe_session_id: sessionId,
            attempt_status: 'abandoned',
            abandonment_reason: reason,
            user_agent: userAgent,
            completed_at: new Date().toISOString()
          });

        if (insertError) {
          console.error('Error inserting payment attempt:', insertError);
        } else {
          console.log('Created new abandoned payment attempt record');
        }
      }
    }

    // Also log in a general cancellation log for analytics
    const cancellationLog = {
      event_type: 'payment_cancellation',
      session_id: sessionId,
      certificate_id: finalCertificateId,
      cancellation_reason: reason,
      user_agent: userAgent,
      timestamp: new Date().toISOString(),
      raw_data: {
        sessionId,
        certificateId,
        reason,
        userAgent,
        headers: Object.fromEntries(req.headers.entries())
      }
    };

    // Store in a simple log table (we can create this if needed)
    // For now, we'll just log to console and the payment_attempts table above
    console.log('Payment cancellation logged:', cancellationLog);

    return new Response(JSON.stringify({
      success: true,
      message: 'Payment cancellation logged successfully',
      certificateId: finalCertificateId
    }), {
      status: 200,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });

  } catch (error) {
    console.error(`Error logging payment cancellation: ${error.message}`);
    
    return new Response(JSON.stringify({
      error: error.message,
      success: false
    }), {
      status: 500,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  }
});
