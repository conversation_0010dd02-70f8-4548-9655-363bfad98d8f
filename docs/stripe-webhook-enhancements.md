# Stripe Webhook Cancellation and Error Handling Enhancements

## Table of Contents
1. [System Overview](#system-overview)
2. [Database Schema Documentation](#database-schema-documentation)
3. [Edge Functions Documentation](#edge-functions-documentation)
4. [Frontend Components Documentation](#frontend-components-documentation)
5. [API Integration Guide](#api-integration-guide)
6. [Admin Dashboard Guide](#admin-dashboard-guide)
7. [Deployment and Configuration](#deployment-and-configuration)
8. [Troubleshooting Guide](#troubleshooting-guide)

## System Overview

The enhanced Stripe webhook system provides comprehensive payment monitoring, error tracking, and cancellation detection for the energy certificate application. The system addresses the critical limitation that <PERSON><PERSON> does not send webhook events when users click the "back" button during checkout.

### Five Main Enhancement Categories

#### 1. **Additional Webhook Events**
- Expanded from 4 to 10 handled webhook event types
- Added support for async payments, charge failures, and disputes
- Enhanced error detection and payment lifecycle tracking

#### 2. **Enhanced Webhook Handler**
- Improved switch statement with comprehensive error handling
- Consistent email notification patterns for all failure scenarios
- Proper payment status updates and database consistency

#### 3. **Error Tracking and Monitoring**
- New `payment_attempts` table for complete lifecycle tracking
- Proactive session monitoring with automated abandonment detection
- Comprehensive logging with session duration and failure reasons

#### 4. **Frontend Enhancements**
- Session timeout warnings (25-minute warning before 30-minute expiration)
- Enhanced error messaging with specific troubleshooting guidance
- Analytics logging for cancellation events and user behavior

#### 5. **Admin Dashboard**
- Real-time payment analytics with auto-refresh functionality
- Webhook event monitoring and delivery status tracking
- Payment attempt summaries with session duration analytics

### Payment Lifecycle Flow

```
User Initiates Payment → Checkout Session Created → Payment Attempt Logged
                                    ↓
                    User Completes/Cancels/Abandons Payment
                                    ↓
                    Webhook Events Processed → Status Updated
                                    ↓
                    Analytics Updated → Admin Dashboard Refreshed
```

## Database Schema Documentation

### Payment Attempts Table

The `payment_attempts` table tracks the complete payment lifecycle for each certificate.

```sql
CREATE TABLE payment_attempts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  certificate_id UUID REFERENCES energieausweise(id) ON DELETE CASCADE,
  stripe_session_id TEXT,
  stripe_payment_intent_id TEXT,
  attempt_status TEXT NOT NULL DEFAULT 'initiated' CHECK (
    attempt_status IN ('initiated', 'processing', 'abandoned', 'failed', 'succeeded', 'expired', 'disputed')
  ),
  payment_method TEXT,
  amount_cents INTEGER,
  currency TEXT DEFAULT 'eur',
  abandonment_reason TEXT,
  failure_reason TEXT,
  user_agent TEXT,
  ip_address INET,
  session_duration_seconds INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Field Definitions

| Field | Type | Description |
|-------|------|-------------|
| `attempt_status` | TEXT | Payment attempt status: `initiated`, `processing`, `abandoned`, `failed`, `succeeded`, `expired`, `disputed` |
| `abandonment_reason` | TEXT | Reason for abandonment: `user_cancelled`, `session_timeout`, `technical_error`, etc. |
| `failure_reason` | TEXT | Specific failure reason from Stripe or internal systems |
| `session_duration_seconds` | INTEGER | Time spent in payment session before completion/abandonment |
| `user_agent` | TEXT | Browser user agent for analytics and debugging |

### Enhanced Payment Status Values

The `energieausweise` table now supports expanded payment status values:

| Status | Description | Trigger |
|--------|-------------|---------|
| `unpaid` | Default status for new certificates | Certificate creation |
| `paid` | Successfully paid certificate | `checkout.session.completed` |
| `failed` | Payment processing failed | `charge.failed`, `payment_intent.payment_failed` |
| `expired` | Session expired without payment | `checkout.session.expired` |
| `disputed` | Chargeback or dispute created | `charge.dispute.created` |

### Payment Analytics Function

```sql
CREATE OR REPLACE FUNCTION get_payment_analytics()
RETURNS JSON
```

#### Return Structure

```json
{
  "total_certificates": 150,
  "paid_certificates": 120,
  "failed_certificates": 15,
  "expired_certificates": 10,
  "disputed_certificates": 2,
  "unpaid_certificates": 3,
  "total_revenue_cents": 588000,
  "conversion_rate": 80.00
}
```

## Edge Functions Documentation

### log-payment-cancellation

**Purpose**: Tracks payment cancellations and abandonment events for analytics.

**Endpoint**: `POST /functions/v1/log-payment-cancellation`

#### Input Parameters

```typescript
interface PaymentCancellationLog {
  sessionId?: string;        // Stripe checkout session ID
  certificateId?: string;    // Energy certificate ID
  reason: string;           // Cancellation reason
  userAgent?: string;       // Browser user agent
  timestamp: string;        // ISO timestamp
}
```

#### Usage Example

```javascript
await supabase.functions.invoke('log-payment-cancellation', {
  body: { 
    sessionId: 'cs_test_123...', 
    reason: 'user_cancelled',
    userAgent: navigator.userAgent,
    timestamp: new Date().toISOString()
  }
});
```

#### Response Format

```json
{
  "success": true,
  "message": "Payment cancellation logged successfully",
  "certificateId": "uuid-here"
}
```

### check-abandoned-sessions

**Purpose**: Proactively monitors and updates abandoned payment sessions by checking Stripe session status.

**Endpoint**: `POST /functions/v1/check-abandoned-sessions`

#### Functionality
- Queries certificates with unpaid status and Stripe session IDs
- Checks actual Stripe session status via API
- Updates payment status and creates payment attempt records
- Handles sessions older than 30 minutes

#### Scheduling Recommendations
- Run every 15-30 minutes via cron job or scheduled function
- Monitor during peak usage hours more frequently
- Consider rate limiting for Stripe API calls

#### Output Interpretation

```json
{
  "success": true,
  "results": {
    "checked": 25,
    "abandoned": 8,
    "stillActive": 12,
    "expired": 3,
    "errors": 2
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Enhanced stripe-webhook

**New Webhook Events Handled**:

1. **checkout.session.async_payment_failed**
   - Updates payment status to `failed`
   - Sends failure notification emails
   - Logs detailed failure information

2. **checkout.session.async_payment_succeeded**
   - Updates payment status to `paid`
   - Generates order number if missing
   - Sends success notification emails

3. **charge.failed**
   - Updates payment status to `failed`
   - Extracts certificate ID from charge metadata
   - Triggers failure notifications

4. **charge.dispute.created**
   - Updates payment status to `disputed`
   - Retrieves charge details for certificate linking
   - Sends dispute notification emails

5. **invoice.payment_failed**
   - Handles invoice-based payment failures
   - Updates certificate status appropriately
   - Sends failure notifications

6. **payment_method.automatically_updated**
   - Logs payment method updates for monitoring
   - No immediate certificate status changes required

#### Processing Logic Flow

```
Webhook Received → Signature Verified → Event Logged → Certificate ID Extracted
                                                              ↓
                    Email Sent ← Status Updated ← Event Processed
```

## Frontend Components Documentation

### Enhanced ZusammenfassungPage.tsx

#### Session Timeout Warnings

**Implementation**:
```typescript
useEffect(() => {
  let timeoutWarning: NodeJS.Timeout;
  let sessionExpiry: NodeJS.Timeout;

  if (energieausweisData && !isProcessingPayment) {
    // Warning at 25 minutes (5 minutes before expiration)
    timeoutWarning = setTimeout(() => {
      setError('Ihre Sitzung läuft in 5 Minuten ab...');
    }, 25 * 60 * 1000);

    // Expiry warning at 30 minutes
    sessionExpiry = setTimeout(() => {
      setError('Ihre Sitzung ist abgelaufen...');
    }, 30 * 60 * 1000);
  }

  return () => {
    if (timeoutWarning) clearTimeout(timeoutWarning);
    if (sessionExpiry) clearTimeout(sessionExpiry);
  };
}, [energieausweisData, isProcessingPayment]);
```

#### Payment Attempt Tracking

**Purpose**: Creates payment attempt records when checkout sessions are initiated.

**Implementation**:
```typescript
const trackPaymentAttempt = async (sessionId: string, sessionUrl: string) => {
  const { error } = await supabase
    .from('payment_attempts')
    .insert({
      certificate_id: activeCertificateId,
      stripe_session_id: sessionId,
      attempt_status: 'initiated',
      amount_cents: 4900,
      currency: 'eur',
      user_agent: navigator.userAgent,
      created_at: new Date().toISOString()
    });
};
```

### Enhanced PaymentCancelPage.tsx

#### Error Categorization

The page now provides specific guidance based on error types:

| Error Code | Description | Guidance |
|------------|-------------|----------|
| `card_declined` | Card was declined by issuer | Check card status, try different card |
| `insufficient_funds` | Insufficient account balance | Check balance, use different payment method |
| `session_expired` | Payment session timed out | Return to summary, restart payment |
| `processing_error` | Technical system error | Wait and retry, contact support if persistent |

#### Analytics Logging

**Implementation**:
```typescript
const trackCancellation = async () => {
  const sessionId = localStorage.getItem('lastCheckoutSessionId');
  if (sessionId) {
    await supabase.functions.invoke('log-payment-cancellation', {
      body: { 
        sessionId, 
        reason: error || 'user_cancelled',
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString()
      }
    });
  }
};
```

### Enhanced AdminPage.tsx

#### Real-time Monitoring Features

**Auto-refresh**: Automatically updates data every 30 seconds when enabled.

**Manual Refresh**: Immediate data refresh with timestamp tracking.

**Implementation**:
```typescript
useEffect(() => {
  let interval: NodeJS.Timeout;
  
  if (autoRefresh) {
    interval = setInterval(() => {
      queryClient.invalidateQueries({ queryKey: ['adminEnergieausweise'] });
      queryClient.invalidateQueries({ queryKey: ['paymentAnalytics'] });
      queryClient.invalidateQueries({ queryKey: ['webhookEventsSummary'] });
      queryClient.invalidateQueries({ queryKey: ['paymentAttemptsSummary'] });
      setLastRefresh(new Date());
    }, 30000);
  }

  return () => {
    if (interval) clearInterval(interval);
  };
}, [autoRefresh, queryClient]);
```

#### Analytics Displays

1. **Payment Metrics Cards**
   - Total certificates, paid, failed, expired, disputed, unpaid
   - Total revenue and conversion rate

2. **Webhook Events Table**
   - Event types, processing status, count, latest occurrence
   - Color-coded status indicators

3. **Payment Attempts Summary**
   - Attempt status distribution
   - Average session duration
   - Latest attempt timestamps

## API Integration Guide

### Stripe Webhook Configuration

#### Required Webhook Events

Configure these events in your Stripe Dashboard → Webhooks:

```
checkout.session.completed
checkout.session.expired
checkout.session.async_payment_failed
checkout.session.async_payment_succeeded
payment_intent.succeeded
payment_intent.payment_failed
charge.failed
charge.dispute.created
invoice.payment_failed
payment_method.automatically_updated
```

#### Webhook Endpoint URL

```
https://your-project.supabase.co/functions/v1/stripe-webhook
```

#### Required Environment Variables

```bash
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJ...
RESEND_API_KEY=re_...
```

### Error Handling and Retry Mechanisms

#### Webhook Idempotency

The system prevents duplicate processing using Stripe event IDs:

```typescript
const alreadyProcessed = await webhookLogger.isEventAlreadyProcessed(receivedEvent.id);
if (alreadyProcessed) {
  return new Response(JSON.stringify({ ok: true, message: 'Event already processed' }));
}
```

#### Retry Logic

- Stripe automatically retries failed webhooks
- Events are logged regardless of processing success
- Failed events can be reprocessed manually via admin dashboard

## Admin Dashboard Guide

### Payment Analytics Interpretation

#### Key Metrics

1. **Conversion Rate**: Percentage of certificates that result in successful payment
   - Formula: `(paid_certificates / total_certificates) * 100`
   - Target: >80% for healthy conversion

2. **Abandonment Rate**: Percentage of initiated payments that are abandoned
   - Monitor for sudden increases indicating UX issues

3. **Failure Rate**: Percentage of payment attempts that fail
   - High rates may indicate payment method or technical issues

#### Webhook Event Status Meanings

| Status | Description | Action Required |
|--------|-------------|-----------------|
| `handled` | Event processed successfully | None |
| `unhandled` | Event received but not processed | Review event type support |
| `error` | Processing failed | Check logs, may need manual intervention |
| `pending` | Event logged but not yet processed | Monitor for completion |

### Using Auto-refresh and Manual Refresh

#### Auto-refresh (Recommended)
- Enables real-time monitoring
- Updates every 30 seconds
- Minimal performance impact
- Can be toggled on/off

#### Manual Refresh
- Immediate data update
- Shows last refresh timestamp
- Useful for investigating specific issues
- Refreshes all dashboard sections simultaneously

### Payment Attempt Analysis

#### Session Duration Insights

- **Short durations (<2 minutes)**: Likely immediate abandonment or technical issues
- **Medium durations (2-10 minutes)**: Normal user consideration time
- **Long durations (>10 minutes)**: Possible user confusion or payment method issues

#### Abandonment Reasons

| Reason | Description | Potential Solutions |
|--------|-------------|-------------------|
| `user_cancelled` | User clicked back/cancel | Improve checkout UX, reduce friction |
| `session_timeout` | 30-minute timeout reached | Add timeout warnings, save progress |
| `technical_error` | System or network error | Investigate error logs, improve reliability |
| `payment_method_error` | Payment method issues | Provide alternative payment options |

## Deployment and Configuration

### Database Migrations Execution Order

Execute migrations in this order:

1. **add_payment_status_values.sql**
   ```bash
   # Adds new payment status values and indexes
   ```

2. **create_payment_attempts_table.sql**
   ```bash
   # Creates payment_attempts table with RLS policies
   ```

3. **create_payment_analytics_function.sql**
   ```bash
   # Creates get_payment_analytics() function
   ```

### Edge Functions Deployment

Deploy functions using Supabase CLI or MCP tools:

```bash
# Deploy webhook enhancements
supabase functions deploy stripe-webhook

# Deploy new functions
supabase functions deploy log-payment-cancellation
supabase functions deploy check-abandoned-sessions
```

### Environment Variables Setup

#### Supabase Project Settings

Add these environment variables in Supabase Dashboard → Settings → Environment Variables:

```
STRIPE_SECRET_KEY=sk_live_... (or sk_test_... for testing)
STRIPE_WEBHOOK_SECRET=whsec_...
RESEND_API_KEY=re_...
```

#### Stripe Dashboard Configuration

1. Navigate to Webhooks section
2. Create new webhook endpoint
3. Add endpoint URL: `https://your-project.supabase.co/functions/v1/stripe-webhook`
4. Select all required events listed above
5. Copy webhook signing secret to environment variables

## Troubleshooting Guide

### Common Issues and Solutions

#### 1. Webhook Events Not Being Processed

**Symptoms**: Events appear in Stripe dashboard but not in database

**Solutions**:
- Verify webhook endpoint URL is correct
- Check webhook signing secret matches environment variable
- Review Supabase function logs for errors
- Ensure all required environment variables are set

#### 2. Payment Status Not Updating

**Symptoms**: Payments complete in Stripe but certificate status remains unpaid

**Solutions**:
- Check webhook event delivery in Stripe dashboard
- Verify certificate ID is correctly passed in `client_reference_id`
- Review webhook processing logs for errors
- Manually trigger webhook redelivery in Stripe

#### 3. Session Timeout Warnings Not Appearing

**Symptoms**: Users don't receive timeout warnings

**Solutions**:
- Verify JavaScript is enabled in browser
- Check browser console for errors
- Ensure component is properly mounted
- Verify timeout logic is not being cleared prematurely

### Monitoring Webhook Delivery Failures

#### Stripe Dashboard Monitoring

1. Navigate to Webhooks → Your Endpoint
2. Check "Recent deliveries" section
3. Look for failed deliveries (4xx, 5xx responses)
4. Review response details for error information

#### Supabase Function Logs

```bash
# View recent logs
supabase functions logs stripe-webhook

# Filter for errors
supabase functions logs stripe-webhook --filter "ERROR"
```

### Debugging Payment Cancellation Tracking

#### Common Debug Steps

1. **Check localStorage**: Verify `lastCheckoutSessionId` is being set
2. **Network Tab**: Confirm cancellation logging API calls are made
3. **Database**: Query `payment_attempts` table for recent entries
4. **Function Logs**: Review `log-payment-cancellation` function logs

#### SQL Debugging Queries

```sql
-- Check recent payment attempts
SELECT * FROM payment_attempts 
WHERE created_at > NOW() - INTERVAL '1 hour'
ORDER BY created_at DESC;

-- Check webhook event processing
SELECT event_type, processing_status, COUNT(*) 
FROM stripe_webhook_events 
WHERE created_at > NOW() - INTERVAL '24 hours'
GROUP BY event_type, processing_status;

-- Check certificates with payment issues
SELECT id, payment_status, stripe_checkout_session_id, created_at
FROM energieausweise 
WHERE payment_status IN ('failed', 'expired', 'disputed')
AND created_at > NOW() - INTERVAL '7 days';
```

### Performance Considerations

#### Database Optimization

- Indexes are automatically created for frequently queried fields
- Consider partitioning `payment_attempts` table for high-volume applications
- Monitor query performance for analytics functions

#### Function Performance

- Webhook processing should complete within 10 seconds
- Consider implementing queue system for high-volume webhook processing
- Monitor function execution time and memory usage

#### Auto-refresh Impact

- 30-second refresh interval balances real-time updates with performance
- Consider increasing interval during low-activity periods
- Monitor database connection usage with multiple admin users

### Monitoring and Alerting

#### Key Metrics to Monitor

1. **Webhook Processing Success Rate**: Should be >99%
2. **Payment Conversion Rate**: Monitor for significant drops
3. **Session Abandonment Rate**: Track trends over time
4. **Function Execution Time**: Ensure consistent performance

#### Recommended Alerts

- Webhook processing failures >5% in 1 hour
- Payment conversion rate drops >10% from baseline
- Function execution time >30 seconds
- Database connection errors

This documentation provides comprehensive coverage of the enhanced Stripe webhook system. For additional support or questions, refer to the Supabase documentation or contact the development team.
