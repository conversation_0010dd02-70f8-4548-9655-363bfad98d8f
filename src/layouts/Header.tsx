import { Link, useNavigate } from '@tanstack/react-router';
import { useAuth } from '../contexts/AuthContext';
import { useUserRole } from '../hooks/useUserRole';

export const Header = () => {
  const { user, isAnonymous, signOut } = useAuth();
  const { data: userRole } = useUserRole();
  const navigate = useNavigate();

  const isAdmin = userRole === 'admin';

  const handleLogout = async () => {
    await signOut();
    navigate({ to: '/' });
  };

  return (
    <header className="bg-white shadow-md">
      <div className="container mx-auto px-4 py-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <Link to="/" className="flex items-center text-xl font-bold text-green-600">
              <img
                src="/Energieberatung.webp"
                alt="Energieausweis Logo"
                className="h-8 mr-2"
              />
              Energieausweis
            </Link>
          </div>
          <nav className="flex space-x-6">
            {user ? (
              <>
                {/* Show different navigation for anonymous vs permanent users */}
                {isAnonymous ? (
                  <>
                    {/* Anonymous user navigation */}
                    <div className="flex items-center space-x-2">
                      <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                        Schnellstart
                      </span>
                      <span className="text-gray-500 text-sm">
                        Energieausweis erstellen
                      </span>
                    </div>
                    <button
                      onClick={handleLogout}
                      className="text-gray-600 hover:text-green-600 transition-colors text-sm"
                    >
                      Sitzung beenden
                    </button>
                  </>
                ) : (
                  <>
                    {/* Permanent user navigation */}
                    <Link
                      to="/meine-zertifikate"
                      className="text-gray-600 hover:text-green-600 transition-colors"
                      activeProps={{ className: 'text-green-600 font-medium' }}
                    >
                      Meine Zertifikate
                    </Link>
                    {isAdmin && (
                      <Link
                        to="/admin"
                        className="text-gray-600 hover:text-green-600 transition-colors"
                        activeProps={{ className: 'text-green-600 font-medium' }}
                      >
                        Admin
                      </Link>
                    )}
                    <Link
                      to="/konto"
                      className="text-gray-600 hover:text-green-600 transition-colors"
                      activeProps={{ className: 'text-green-600 font-medium' }}
                    >
                      Konto
                    </Link>
                    <button
                      onClick={handleLogout}
                      className="text-gray-600 hover:text-green-600 transition-colors"
                    >
                      Abmelden
                    </button>
                  </>
                )}
              </>
            ) : (
              <Link
                to="/login"
                className="text-gray-600 hover:text-green-600 transition-colors"
                activeProps={{ className: 'text-green-600 font-medium' }}
              >
                Login
              </Link>
            )}
          </nav>
        </div>
      </div>
    </header>
  );
};
