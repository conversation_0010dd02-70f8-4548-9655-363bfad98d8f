<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Dimension Validation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #007acc;
        }
        .success { border-left-color: #28a745; background: #d4edda; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        .info { border-left-color: #17a2b8; background: #d1ecf1; }
        
        .file-input {
            margin: 10px 0;
            padding: 10px;
            border: 2px dashed #ccc;
            border-radius: 4px;
            text-align: center;
        }
        
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
        }
        
        .result.valid {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .result.invalid {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #005a9e;
        }
        
        .image-preview {
            max-width: 200px;
            max-height: 150px;
            border: 1px solid #ddd;
            margin: 10px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        th {
            background: #f2f2f2;
        }
    </style>
</head>
<body>
    <h1>🖼️ Image Dimension Validation Test</h1>
    
    <div class="test-section info">
        <h2>📋 Test Overview</h2>
        <p>This page tests the image dimension validation functionality for building image uploads (gebaeudebild) in the DirectoryBasedFileUpload component.</p>
        
        <h3>Requirements:</h3>
        <ul>
            <li><strong>Minimum dimensions:</strong> 360 x 240 pixels (6cm x 4cm at 60px/cm)</li>
            <li><strong>Maximum dimensions:</strong> 4000 x 3000 pixels</li>
            <li><strong>Supported formats:</strong> JPG, PNG, WebP</li>
            <li><strong>Maximum file size:</strong> 5 MB</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🧪 Interactive Test</h2>
        <p>Select an image file to test the dimension validation:</p>
        
        <div class="file-input">
            <input type="file" id="imageInput" accept=".jpg,.jpeg,.png,.webp" />
            <p>Choose an image file to validate</p>
        </div>
        
        <div id="results"></div>
        <div id="imagePreview"></div>
    </div>

    <div class="test-section">
        <h2>📊 Test Cases</h2>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Dimensions</th>
                    <th>Expected Result</th>
                    <th>Reason</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Valid Image</td>
                    <td>800 x 600</td>
                    <td class="result valid">✅ Valid</td>
                    <td>Within acceptable range</td>
                </tr>
                <tr>
                    <td>Too Small</td>
                    <td>300 x 200</td>
                    <td class="result invalid">❌ Invalid</td>
                    <td>Below minimum 360x240</td>
                </tr>
                <tr>
                    <td>Too Large</td>
                    <td>5000 x 4000</td>
                    <td class="result invalid">❌ Invalid</td>
                    <td>Above maximum 4000x3000</td>
                </tr>
                <tr>
                    <td>Minimum Valid</td>
                    <td>360 x 240</td>
                    <td class="result valid">✅ Valid</td>
                    <td>Exactly at minimum</td>
                </tr>
                <tr>
                    <td>Maximum Valid</td>
                    <td>4000 x 3000</td>
                    <td class="result valid">✅ Valid</td>
                    <td>Exactly at maximum</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="test-section">
        <h2>🔧 Manual Testing Steps</h2>
        <ol>
            <li><strong>Test Valid Image:</strong>
                <ul>
                    <li>Upload an image with dimensions between 360x240 and 4000x3000</li>
                    <li>Should show success message and allow upload</li>
                </ul>
            </li>
            <li><strong>Test Small Image:</strong>
                <ul>
                    <li>Upload an image smaller than 360x240 pixels</li>
                    <li>Should show error message with current and required dimensions</li>
                </ul>
            </li>
            <li><strong>Test Large Image:</strong>
                <ul>
                    <li>Upload an image larger than 4000x3000 pixels</li>
                    <li>Should show error message with current and maximum dimensions</li>
                </ul>
            </li>
            <li><strong>Test Non-Image File:</strong>
                <ul>
                    <li>Try uploading a PDF or other non-image file</li>
                    <li>Should not trigger dimension validation</li>
                </ul>
            </li>
        </ol>
    </div>

    <div class="test-section warning">
        <h2>⚠️ Important Notes</h2>
        <ul>
            <li><strong>Field-Specific:</strong> Dimension validation only applies to 'gebaeudebild' field</li>
            <li><strong>Client-Side:</strong> Validation happens before upload to prevent unnecessary server requests</li>
            <li><strong>User-Friendly:</strong> Error messages are in German and provide specific dimension information</li>
            <li><strong>File Reset:</strong> Invalid files are rejected and input is reset for new selection</li>
        </ul>
    </div>

    <script>
        // Image dimension validation constants (matching component)
        const IMAGE_DIMENSION_REQUIREMENTS = {
            minWidth: 360,   // 6cm * 60px/cm
            minHeight: 240,  // 4cm * 60px/cm
            maxWidth: 4000,  // Maximum reasonable width
            maxHeight: 3000  // Maximum reasonable height
        };

        // Validate image dimensions
        function validateImageDimensions(file) {
            return new Promise((resolve) => {
                if (!file.type.startsWith('image/')) {
                    resolve({ valid: true, message: 'Not an image file - no dimension validation needed' });
                    return;
                }

                const img = new Image();
                const url = URL.createObjectURL(file);

                img.onload = () => {
                    URL.revokeObjectURL(url);
                    
                    const { width, height } = img;
                    const { minWidth, minHeight, maxWidth, maxHeight } = IMAGE_DIMENSION_REQUIREMENTS;

                    if (width < minWidth || height < minHeight) {
                        resolve({
                            valid: false,
                            message: `${file.name}: Bild zu klein (mindestens ${minWidth}x${minHeight} Pixel erforderlich, aktuell: ${width}x${height} Pixel)`
                        });
                    } else if (width > maxWidth || height > maxHeight) {
                        resolve({
                            valid: false,
                            message: `${file.name}: Bild zu groß (maximal ${maxWidth}x${maxHeight} Pixel erlaubt, aktuell: ${width}x${height} Pixel)`
                        });
                    } else {
                        resolve({
                            valid: true,
                            message: `${file.name}: Gültige Bildabmessungen (${width}x${height} Pixel)`
                        });
                    }
                };

                img.onerror = () => {
                    URL.revokeObjectURL(url);
                    resolve({
                        valid: false,
                        message: `${file.name}: Bilddatei konnte nicht gelesen werden`
                    });
                };

                img.src = url;
            });
        }

        // Handle file input change
        document.getElementById('imageInput').addEventListener('change', async (e) => {
            const file = e.target.files[0];
            if (!file) return;

            const resultsDiv = document.getElementById('results');
            const previewDiv = document.getElementById('imagePreview');
            
            // Clear previous results
            resultsDiv.innerHTML = '<p>Validating...</p>';
            previewDiv.innerHTML = '';

            try {
                // Validate dimensions
                const result = await validateImageDimensions(file);
                
                // Display result
                resultsDiv.innerHTML = `
                    <div class="result ${result.valid ? 'valid' : 'invalid'}">
                        <strong>${result.valid ? '✅ Valid' : '❌ Invalid'}:</strong> ${result.message}
                    </div>
                    <p><strong>File size:</strong> ${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                    <p><strong>File type:</strong> ${file.type}</p>
                `;

                // Show image preview if it's an image
                if (file.type.startsWith('image/')) {
                    const img = document.createElement('img');
                    img.src = URL.createObjectURL(file);
                    img.className = 'image-preview';
                    img.onload = () => URL.revokeObjectURL(img.src);
                    previewDiv.appendChild(img);
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="result invalid">
                        <strong>❌ Error:</strong> ${error.message}
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
