// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.
// Import via bare specifier thanks to the import_map.json file.
import Stripe from 'https://esm.sh/stripe@14?target=denonext';
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.49.1";
import { Resend } from "https://esm.sh/resend@2.1.0";
const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY'), {
  // This is needed to use the Fetch API rather than relying on the Node http
  // package.
  apiVersion: '2024-11-20'
});
const webhookSecret = Deno.env.get("STRIPE_WEBHOOK_SECRET");
const cryptoProvider = Stripe.createSubtleCryptoProvider();
// Create Supabase client with service role key for admin access
const supabaseUrl = Deno.env.get("SUPABASE_URL");
const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");
if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error("Missing Supabase credentials");
}
const supabase = createClient(supabaseUrl, supabaseServiceKey);
class EmailService {
  resend;
  supabase;
  fromEmail;
  adminEmail;
  constructor(){
    const resendApiKey = Deno.env.get("RESEND_API_KEY");
    const supabaseUrl = Deno.env.get("SUPABASE_URL");
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");
    this.fromEmail = Deno.env.get("FROM_EMAIL");
    this.adminEmail = Deno.env.get("ADMIN_EMAIL");
    if (!resendApiKey) {
      throw new Error("RESEND_API_KEY environment variable is required");
    }
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error("Supabase environment variables are required");
    }
    this.resend = new Resend(resendApiKey);
    this.supabase = createClient(supabaseUrl, supabaseServiceKey);
  }
  // Log email attempt to database
  async logEmail(emailLog) {
    try {
      const { data, error } = await this.supabase.from('email_logs').insert(emailLog).select('id').single();
      if (error) {
        console.error('Error logging email:', error);
        return null;
      }
      return data?.id || null;
    } catch (error) {
      console.error('Error logging email:', error);
      return null;
    }
  }
  // Update email log status
  async updateEmailLog(logId, updates) {
    try {
      const { error } = await this.supabase.from('email_logs').update(updates).eq('id', logId);
      if (error) {
        console.error('Error updating email log:', error);
      }
    } catch (error) {
      console.error('Error updating email log:', error);
    }
  }
  // Get user email from auth.users table
  async getUserEmail(userId) {
    try {
      const { data, error } = await this.supabase.auth.admin.getUserById(userId);
      if (error || !data?.user?.email) {
        console.error('Error fetching user email:', error);
        return null;
      }
      return data.user.email;
    } catch (error) {
      console.error('Error fetching user email:', error);
      return null;
    }
  }
  // Get certificate and user data
  async getCertificateData(certificateId) {
    try {
      const { data: certificate, error: certError } = await this.supabase.from('energieausweise').select('*').eq('id', certificateId).single();
      if (certError || !certificate) {
        console.error('Error fetching certificate:', certError);
        return null;
      }
      const userEmail = await this.getUserEmail(certificate.user_id);
      if (!userEmail) {
        console.error('Could not fetch user email for certificate:', certificateId);
        return null;
      }
      return {
        certificate,
        userEmail
      };
    } catch (error) {
      console.error('Error fetching certificate data:', error);
      return null;
    }
  }
  // Generate customer success email content
  generateCustomerSuccessEmail(certificate, userEmail) {
    const certificateTypeMap = {
      'WG/V': 'Wohngebäude Verbrauchsausweis',
      'WG/B': 'Wohngebäude Bedarfsausweis',
      'NWG/V': 'Nichtwohngebäude Verbrauchsausweis'
    };
    const certificateTypeName = certificateTypeMap[certificate.certificate_type] || certificate.certificate_type;
    const buildingAddress = certificate.objektdaten?.strasse && certificate.objektdaten?.hausnummer ? `${certificate.objektdaten.strasse} ${certificate.objektdaten.hausnummer}, ${certificate.objektdaten.plz} ${certificate.objektdaten.ort}` : 'Ihre Immobilie';
    const subject = `Zahlungsbestätigung - Energieausweis ${certificate.order_number}`;
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Zahlungsbestätigung</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #16a34a; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Zahlungsbestätigung</h1>
          </div>
          <div class="content">
            <p>Vielen Dank für Ihre Bestellung! Ihre Zahlung wurde erfolgreich verarbeitet.</p>

            <div class="details">
              <h3>Bestelldetails:</h3>
              <p><strong>Bestellnummer:</strong> ${certificate.order_number}</p>
              <p><strong>Energieausweis-Typ:</strong> ${certificateTypeName}</p>
              <p><strong>Immobilie:</strong> ${buildingAddress}</p>
              <p><strong>Bestelldatum:</strong> ${new Date(certificate.created_at).toLocaleDateString('de-DE')}</p>
            </div>

            <p>Ihr Energieausweis wird nun erstellt und Sie erhalten ihn in Kürze per E-Mail.</p>

            <p>Bei Fragen stehen wir Ihnen gerne zur Verfügung.</p>

            <p>Mit freundlichen Grüßen<br>
            Ihr Energieausweis-Team</p>
          </div>
          <div class="footer">
            <p>Diese E-Mail wurde automatisch generiert. Bitte antworten Sie nicht auf diese E-Mail.</p>
          </div>
        </div>
      </body>
      </html>
    `;
    return {
      subject,
      html
    };
  }
  // Generate customer failure email content
  generateCustomerFailureEmail(certificate, userEmail) {
    const subject = `Zahlungsproblem - Energieausweis ${certificate.order_number}`;
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Zahlungsproblem</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #dc2626; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
          .retry-button { background-color: #16a34a; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 15px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Zahlungsproblem</h1>
          </div>
          <div class="content">
            <p>Leider konnte Ihre Zahlung für den Energieausweis nicht verarbeitet werden.</p>

            <div class="details">
              <h3>Bestelldetails:</h3>
              <p><strong>Bestellnummer:</strong> ${certificate.order_number}</p>
              <p><strong>Bestelldatum:</strong> ${new Date(certificate.created_at).toLocaleDateString('de-DE')}</p>
            </div>

            <p>Mögliche Gründe für das Zahlungsproblem:</p>
            <ul>
              <li>Unzureichende Deckung auf dem Konto</li>
              <li>Falsche Kartendaten</li>
              <li>Technisches Problem bei der Zahlungsabwicklung</li>
            </ul>

            <p>Sie können die Zahlung jederzeit erneut versuchen:</p>
            <a href="${Deno.env.get('SITE_URL') || 'https://k2-energieausweis.de'}/meine-zertifikate" class="retry-button">Zahlung erneut versuchen</a>

            <p>Bei anhaltenden Problemen kontaktieren Sie uns bitte.</p>

            <p>Mit freundlichen Grüßen<br>
            Ihr Energieausweis-Team</p>
          </div>
          <div class="footer">
            <p>Diese E-Mail wurde automatisch generiert. Bitte antworten Sie nicht auf diese E-Mail.</p>
          </div>
        </div>
      </body>
      </html>
    `;
    return {
      subject,
      html
    };
  }
  // Generate admin success email content
  generateAdminSuccessEmail(certificate, userEmail) {
    const certificateTypeMap = {
      'WG/V': 'Wohngebäude Verbrauchsausweis',
      'WG/B': 'Wohngebäude Bedarfsausweis',
      'NWG/V': 'Nichtwohngebäude Verbrauchsausweis'
    };
    const certificateTypeName = certificateTypeMap[certificate.certificate_type] || certificate.certificate_type;
    const buildingAddress = certificate.objektdaten?.strasse && certificate.objektdaten?.hausnummer ? `${certificate.objektdaten.strasse} ${certificate.objektdaten.hausnummer}, ${certificate.objektdaten.plz} ${certificate.objektdaten.ort}` : 'Nicht verfügbar';
    const subject = `Neue Bestellung - Energieausweis ${certificate.order_number}`;
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Neue Bestellung</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 700px; margin: 0 auto; padding: 20px; }
          .header { background-color: #1f2937; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
          table { width: 100%; border-collapse: collapse; margin: 10px 0; }
          th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
          th { background-color: #f8f9fa; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Neue Energieausweis-Bestellung</h1>
          </div>
          <div class="content">
            <p>Eine neue Bestellung ist eingegangen und wurde erfolgreich bezahlt.</p>

            <div class="details">
              <h3>Bestellinformationen:</h3>
              <table>
                <tr><th>Bestellnummer</th><td>${certificate.order_number}</td></tr>
                <tr><th>Zertifikat-ID</th><td>${certificate.id}</td></tr>
                <tr><th>Energieausweis-Typ</th><td>${certificateTypeName}</td></tr>
                <tr><th>Kunde E-Mail</th><td>${userEmail}</td></tr>
                <tr><th>Bestelldatum</th><td>${new Date(certificate.created_at).toLocaleDateString('de-DE', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })}</td></tr>
                <tr><th>Zahlungsstatus</th><td>${certificate.payment_status}</td></tr>
              </table>
            </div>

            <div class="details">
              <h3>Immobilieninformationen:</h3>
              <table>
                <tr><th>Adresse</th><td>${buildingAddress}</td></tr>
                ${certificate.objektdaten?.baujahr ? `<tr><th>Baujahr</th><td>${certificate.objektdaten.baujahr}</td></tr>` : ''}
                ${certificate.objektdaten?.wohnflaeche ? `<tr><th>Wohnfläche</th><td>${certificate.objektdaten.wohnflaeche} m²</td></tr>` : ''}
                ${certificate.objektdaten?.anzahlWohneinheiten ? `<tr><th>Anzahl Wohneinheiten</th><td>${certificate.objektdaten.anzahlWohneinheiten}</td></tr>` : ''}
              </table>
            </div>

            <p><strong>Nächste Schritte:</strong></p>
            <ul>
              <li>Energieausweis erstellen</li>
              <li>Qualitätsprüfung durchführen</li>
              <li>Energieausweis an Kunden senden</li>
            </ul>

            <p>Bestellung im Admin-Dashboard anzeigen: <a href="${Deno.env.get('SITE_URL') || 'https://k2-energieausweis.de'}/admin">Admin-Dashboard</a></p>
          </div>
          <div class="footer">
            <p>Diese E-Mail wurde automatisch generiert.</p>
          </div>
        </div>
      </body>
      </html>
    `;
    return {
      subject,
      html
    };
  }
  // Generate admin failure email content
  generateAdminFailureEmail(certificate, userEmail) {
    const subject = `Zahlungsfehlschlag - Energieausweis ${certificate.order_number}`;
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Zahlungsfehlschlag</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #dc2626; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
          table { width: 100%; border-collapse: collapse; margin: 10px 0; }
          th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
          th { background-color: #f8f9fa; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Zahlungsfehlschlag</h1>
          </div>
          <div class="content">
            <p>Eine Zahlung für eine Energieausweis-Bestellung ist fehlgeschlagen.</p>

            <div class="details">
              <h3>Bestellinformationen:</h3>
              <table>
                <tr><th>Bestellnummer</th><td>${certificate.order_number}</td></tr>
                <tr><th>Zertifikat-ID</th><td>${certificate.id}</td></tr>
                <tr><th>Kunde E-Mail</th><td>${userEmail}</td></tr>
                <tr><th>Bestelldatum</th><td>${new Date(certificate.created_at).toLocaleDateString('de-DE')}</td></tr>
                <tr><th>Zahlungsstatus</th><td>${certificate.payment_status}</td></tr>
              </table>
            </div>

            <p><strong>Erforderliche Maßnahmen:</strong></p>
            <ul>
              <li>Kunde wurde über den Zahlungsfehlschlag informiert</li>
              <li>Kunde kann Zahlung erneut versuchen</li>
              <li>Bei wiederholten Problemen Kunde kontaktieren</li>
            </ul>

            <p>Bestellung im Admin-Dashboard anzeigen: <a href="${Deno.env.get('SITE_URL') || 'https://k2-energieausweis.de'}/admin">Admin-Dashboard</a></p>
          </div>
          <div class="footer">
            <p>Diese E-Mail wurde automatisch generiert.</p>
          </div>
        </div>
      </body>
      </html>
    `;
    return {
      subject,
      html
    };
  }
  // Send email using Resend
  async sendEmail(to, subject, html) {
    try {
      const { data, error } = await this.resend.emails.send({
        from: this.fromEmail,
        to: [
          to
        ],
        subject,
        html
      });
      if (error) {
        console.error('Resend error:', error);
        return {
          success: false,
          error: error.message || 'Unknown Resend error'
        };
      }
      return {
        success: true,
        messageId: data?.id
      };
    } catch (error) {
      console.error('Email sending error:', error);
      return {
        success: false,
        error: error.message || 'Unknown email sending error'
      };
    }
  }
  // Send customer success notification
  async sendCustomerSuccessNotification(certificateId) {
    try {
      const data = await this.getCertificateData(certificateId);
      if (!data) {
        console.error('Could not fetch certificate data for customer success notification');
        return false;
      }
      const { certificate, userEmail } = data;
      const { subject, html } = this.generateCustomerSuccessEmail(certificate, userEmail);
      // Log email attempt
      const logId = await this.logEmail({
        certificate_id: certificateId,
        recipient_email: userEmail,
        email_type: 'customer_success',
        status: 'pending'
      });
      // Send email
      const result = await this.sendEmail(userEmail, subject, html);
      // Update log
      if (logId) {
        await this.updateEmailLog(logId, {
          status: result.success ? 'sent' : 'failed',
          sent_at: result.success ? new Date().toISOString() : undefined,
          error_message: result.error,
          resend_message_id: result.messageId
        });
      }
      if (!result.success) {
        console.error('Failed to send customer success email:', result.error);
        return false;
      }
      console.log(`Customer success email sent to ${userEmail} for certificate ${certificateId}`);
      return true;
    } catch (error) {
      console.error('Error sending customer success notification:', error);
      return false;
    }
  }
  // Send customer failure notification
  async sendCustomerFailureNotification(certificateId) {
    try {
      const data = await this.getCertificateData(certificateId);
      if (!data) {
        console.error('Could not fetch certificate data for customer failure notification');
        return false;
      }
      const { certificate, userEmail } = data;
      const { subject, html } = this.generateCustomerFailureEmail(certificate, userEmail);
      // Log email attempt
      const logId = await this.logEmail({
        certificate_id: certificateId,
        recipient_email: userEmail,
        email_type: 'customer_failure',
        status: 'pending'
      });
      // Send email
      const result = await this.sendEmail(userEmail, subject, html);
      // Update log
      if (logId) {
        await this.updateEmailLog(logId, {
          status: result.success ? 'sent' : 'failed',
          sent_at: result.success ? new Date().toISOString() : undefined,
          error_message: result.error,
          resend_message_id: result.messageId
        });
      }
      if (!result.success) {
        console.error('Failed to send customer failure email:', result.error);
        return false;
      }
      console.log(`Customer failure email sent to ${userEmail} for certificate ${certificateId}`);
      return true;
    } catch (error) {
      console.error('Error sending customer failure notification:', error);
      return false;
    }
  }
  // Send admin success notification
  async sendAdminSuccessNotification(certificateId) {
    try {
      const data = await this.getCertificateData(certificateId);
      if (!data) {
        console.error('Could not fetch certificate data for admin success notification');
        return false;
      }
      const { certificate, userEmail } = data;
      const { subject, html } = this.generateAdminSuccessEmail(certificate, userEmail);
      // Log email attempt
      const logId = await this.logEmail({
        certificate_id: certificateId,
        recipient_email: this.adminEmail,
        email_type: 'admin_success',
        status: 'pending'
      });
      // Send email
      const result = await this.sendEmail(this.adminEmail, subject, html);
      // Update log
      if (logId) {
        await this.updateEmailLog(logId, {
          status: result.success ? 'sent' : 'failed',
          sent_at: result.success ? new Date().toISOString() : undefined,
          error_message: result.error,
          resend_message_id: result.messageId
        });
      }
      if (!result.success) {
        console.error('Failed to send admin success email:', result.error);
        return false;
      }
      console.log(`Admin success email sent to ${this.adminEmail} for certificate ${certificateId}`);
      return true;
    } catch (error) {
      console.error('Error sending admin success notification:', error);
      return false;
    }
  }
  // Send admin failure notification
  async sendAdminFailureNotification(certificateId) {
    try {
      const data = await this.getCertificateData(certificateId);
      if (!data) {
        console.error('Could not fetch certificate data for admin failure notification');
        return false;
      }
      const { certificate, userEmail } = data;
      const { subject, html } = this.generateAdminFailureEmail(certificate, userEmail);
      // Log email attempt
      const logId = await this.logEmail({
        certificate_id: certificateId,
        recipient_email: this.adminEmail,
        email_type: 'admin_failure',
        status: 'pending'
      });
      // Send email
      const result = await this.sendEmail(this.adminEmail, subject, html);
      // Update log
      if (logId) {
        await this.updateEmailLog(logId, {
          status: result.success ? 'sent' : 'failed',
          sent_at: result.success ? new Date().toISOString() : undefined,
          error_message: result.error,
          resend_message_id: result.messageId
        });
      }
      if (!result.success) {
        console.error('Failed to send admin failure email:', result.error);
        return false;
      }
      console.log(`Admin failure email sent to ${this.adminEmail} for certificate ${certificateId}`);
      return true;
    } catch (error) {
      console.error('Error sending admin failure notification:', error);
      return false;
    }
  }
  // Send all notifications for payment success
  async sendPaymentSuccessNotifications(certificateId) {
    const customerSent = await this.sendCustomerSuccessNotification(certificateId);
    const adminSent = await this.sendAdminSuccessNotification(certificateId);
    return {
      customerSent,
      adminSent
    };
  }
  // Send all notifications for payment failure
  async sendPaymentFailureNotifications(certificateId) {
    const customerSent = await this.sendCustomerFailureNotification(certificateId);
    const adminSent = await this.sendAdminFailureNotification(certificateId);
    return {
      customerSent,
      adminSent
    };
  }
}
class WebhookEventLogger {
  supabase;
  constructor(){
    const supabaseUrl = Deno.env.get("SUPABASE_URL");
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error("Supabase environment variables are required for webhook logging");
    }
    this.supabase = createClient(supabaseUrl, supabaseServiceKey);
  }
  // Extract certificate ID from various Stripe event types
  extractCertificateId(event) {
    try {
      // Handle checkout session events
      if (event.type.startsWith('checkout.session.')) {
        const session = event.data.object;
        if (session.client_reference_id) {
          return {
            certificateId: session.client_reference_id,
            method: 'client_reference_id'
          };
        }
        if (session.metadata?.certificate_id) {
          return {
            certificateId: session.metadata.certificate_id,
            method: 'session_metadata'
          };
        }
      }
      // Handle payment intent events
      if (event.type.startsWith('payment_intent.')) {
        const paymentIntent = event.data.object;
        if (paymentIntent.metadata?.certificate_id) {
          return {
            certificateId: paymentIntent.metadata.certificate_id,
            method: 'payment_intent_metadata'
          };
        }
      }
      // Handle invoice events
      if (event.type.startsWith('invoice.')) {
        const invoice = event.data.object;
        if (invoice.metadata?.certificate_id) {
          return {
            certificateId: invoice.metadata.certificate_id,
            method: 'invoice_metadata'
          };
        }
      }
      // Handle subscription events
      if (event.type.startsWith('customer.subscription.')) {
        const subscription = event.data.object;
        if (subscription.metadata?.certificate_id) {
          return {
            certificateId: subscription.metadata.certificate_id,
            method: 'subscription_metadata'
          };
        }
      }
      // Handle charge events
      if (event.type.startsWith('charge.')) {
        const charge = event.data.object;
        if (charge.metadata?.certificate_id) {
          return {
            certificateId: charge.metadata.certificate_id,
            method: 'charge_metadata'
          };
        }
      }
      return {
        certificateId: null,
        method: null
      };
    } catch (error) {
      console.error('Error extracting certificate ID:', error);
      return {
        certificateId: null,
        method: null
      };
    }
  }
  // Log webhook event to database
  async logEvent(event) {
    try {
      const { certificateId, method } = this.extractCertificateId(event);
      const eventLog = {
        stripe_event_id: event.id,
        event_type: event.type,
        certificate_id: certificateId || undefined,
        processing_status: 'pending',
        event_created_at: new Date(event.created * 1000).toISOString(),
        raw_event_data: event,
        certificate_extraction_method: method || undefined
      };
      const { data, error } = await this.supabase.from('stripe_webhook_events').insert(eventLog).select('id').single();
      if (error) {
        console.error('Error logging webhook event:', error);
        return null;
      }
      console.log(`✅ Webhook event logged: ${event.id} (${event.type}) - Log ID: ${data?.id}`);
      return data?.id || null;
    } catch (error) {
      console.error('Error logging webhook event:', error);
      return null;
    }
  }
  // Update event processing status
  async updateEventStatus(logId, status, errorMessage) {
    try {
      const updates = {
        processing_status: status,
        error_message: errorMessage
      };
      const { error } = await this.supabase.from('stripe_webhook_events').update(updates).eq('id', logId);
      if (error) {
        console.error('Error updating webhook event status:', error);
      } else {
        console.log(`✅ Updated webhook event status: ${logId} -> ${status}`);
      }
    } catch (error) {
      console.error('Error updating webhook event status:', error);
    }
  }
  // Check if event was already processed (idempotency)
  async isEventAlreadyProcessed(stripeEventId) {
    try {
      const { data, error } = await this.supabase.from('stripe_webhook_events').select('id, processing_status').eq('stripe_event_id', stripeEventId).single();
      if (error && error.code !== 'PGRST116') {
        console.error('Error checking event processing status:', error);
        return false;
      }
      if (data) {
        console.log(`⚠️ Event ${stripeEventId} already processed with status: ${data.processing_status}`);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error checking event processing status:', error);
      return false;
    }
  }
}
// Initialize services
let emailService = null;
let webhookLogger = null;
try {
  emailService = new EmailService();
} catch (emailError) {
  console.warn('Email service initialization failed:', emailError);
// Continue without email service - webhook should still work for payment processing
}
try {
  webhookLogger = new WebhookEventLogger();
} catch (loggerError) {
  console.warn('Webhook logger initialization failed:', loggerError);
// Continue without webhook logger - webhook should still work for payment processing
}
Deno.serve(async (request)=>{
  const signature = request.headers.get('Stripe-Signature');
  if (!signature) {
    throw new Error("Missing Stripe signature");
  }
  // First step is to verify the event. The .text() method must be used as the
  // verification relies on the raw request body rather than the parsed JSON.
  const body = await request.text();
  console.log('body: ', body);
  let receivedEvent;
  try {
    receivedEvent = await stripe.webhooks.constructEventAsync(body, signature, webhookSecret, undefined, cryptoProvider);
    console.log(`✅ Signature verification successful ${receivedEvent.id} - ${receivedEvent.type}`);
  } catch (err) {
    console.error(`❌ Signature verification NOT successful`, err.message);
    return new Response(err.message, {
      status: 400
    });
  }
  // Log the webhook event (regardless of whether we handle it or not)
  let eventLogId = null;
  if (webhookLogger) {
    try {
      // Check if event was already processed (idempotency)
      const alreadyProcessed = await webhookLogger.isEventAlreadyProcessed(receivedEvent.id);
      if (alreadyProcessed) {
        console.log(`⚠️ Event ${receivedEvent.id} already processed, skipping...`);
        return new Response(JSON.stringify({
          ok: true,
          message: 'Event already processed'
        }), {
          status: 200
        });
      }
      eventLogId = await webhookLogger.logEvent(receivedEvent);
    } catch (logError) {
      console.error('Error logging webhook event:', logError);
    // Continue processing even if logging fails
    }
  }
  // Handle the receivedEvent
  let processingStatus = 'unhandled';
  let processingError;
  try {
    switch(receivedEvent.type){
      case 'checkout.session.completed':
        {
          const session = receivedEvent.data.object;
          console.log(`Payment completed for session: ${session.id}`);
          console.log(`Client reference ID: ${session.client_reference_id}`);
          // Update payment status in database using the client_reference_id (certificate ID)
          if (session.client_reference_id) {
            // First, get the current certificate to check if order_number exists
            const { data: currentCert, error: fetchError } = await supabase.from('energieausweise').select('order_number').eq('id', session.client_reference_id).single();
            if (fetchError) {
              console.error('Error fetching certificate:', fetchError);
              throw new Error(`Failed to fetch certificate: ${fetchError.message}`);
            }
            // Generate order_number if it doesn't exist
            const orderNumber = currentCert?.order_number || `EA-${session.client_reference_id.slice(-8).toUpperCase()}`;
            const { data, error } = await supabase.from('energieausweise').update({
              payment_status: 'paid',
              stripe_checkout_session_id: session.id,
              order_number: orderNumber,
              updated_at: new Date().toISOString()
            }).eq('id', session.client_reference_id).select();
            if (error) {
              console.error('Error updating payment status:', error);
              throw new Error(`Failed to update payment status: ${error.message}`);
            }
            console.log(`Successfully updated payment status for certificate: ${session.client_reference_id}`);
            console.log(`Order number set to: ${orderNumber}`);
            // Send email notifications
            if (emailService) {
              try {
                console.log('Sending payment success email notifications...');
                const emailResults = await emailService.sendPaymentSuccessNotifications(session.client_reference_id);
                console.log('Email notification results:', emailResults);
                if (!emailResults.customerSent) {
                  console.warn('Failed to send customer success email');
                }
                if (!emailResults.adminSent) {
                  console.warn('Failed to send admin success email');
                }
              } catch (emailError) {
                console.error('Error sending email notifications:', emailError);
              // Don't fail the webhook if email sending fails
              }
            } else {
              console.warn('Email service not available - skipping email notifications');
            }
          } else {
            console.warn('No client_reference_id found in session');
          }
          processingStatus = 'handled';
          break;
        }
      case 'checkout.session.expired':
        {
          const session = receivedEvent.data.object;
          console.log(`Payment session expired: ${session.id}`);
          // Update payment status to expired if needed
          if (session.client_reference_id) {
            const { data, error } = await supabase.from('energieausweise').update({
              payment_status: 'expired',
              stripe_checkout_session_id: session.id,
              updated_at: new Date().toISOString()
            }).eq('id', session.client_reference_id).select();
            if (error) {
              console.error('Error updating payment status to expired:', error);
            } else {
              console.log(`Updated payment status to expired for certificate: ${session.client_reference_id}`);
              // Send email notifications for expired session
              if (emailService) {
                try {
                  console.log('Sending payment failure email notifications for expired session...');
                  const emailResults = await emailService.sendPaymentFailureNotifications(session.client_reference_id);
                  console.log('Email notification results for expired session:', emailResults);
                  if (!emailResults.customerSent) {
                    console.warn('Failed to send customer failure email for expired session');
                  }
                  if (!emailResults.adminSent) {
                    console.warn('Failed to send admin failure email for expired session');
                  }
                } catch (emailError) {
                  console.error('Error sending email notifications for expired session:', emailError);
                // Don't fail the webhook if email sending fails
                }
              }
            }
          }
          processingStatus = 'handled';
          break;
        }
      case 'payment_intent.succeeded':
        {
          const paymentIntent = receivedEvent.data.object;
          console.log(`Payment intent succeeded: ${paymentIntent.id}`);
          processingStatus = 'handled';
          break;
        }
      case 'payment_intent.payment_failed':
        {
          const paymentIntent = receivedEvent.data.object;
          console.log(`Payment intent failed: ${paymentIntent.id}`);
          // Try to find the certificate ID from metadata
          const certificateId = paymentIntent.metadata?.certificate_id;
          if (certificateId && emailService) {
            try {
              console.log('Sending payment failure email notifications for failed payment intent...');
              const emailResults = await emailService.sendPaymentFailureNotifications(certificateId);
              console.log('Email notification results for failed payment intent:', emailResults);
              if (!emailResults.customerSent) {
                console.warn('Failed to send customer failure email for failed payment intent');
              }
              if (!emailResults.adminSent) {
                console.warn('Failed to send admin failure email for failed payment intent');
              }
            } catch (emailError) {
              console.error('Error sending email notifications for failed payment intent:', emailError);
            // Don't fail the webhook if email sending fails
            }
          } else if (!certificateId) {
            console.warn('No certificate_id found in payment intent metadata');
          }
          processingStatus = 'handled';
          break;
        }
      default:
        console.log(`Unhandled event type: ${receivedEvent.type}`);
        processingStatus = 'unhandled';
    }
  } catch (error) {
    console.error('Error processing webhook event:', error);
    processingStatus = 'error';
    processingError = error.message || 'Unknown processing error';
  }
  // Update the event log with processing status
  if (webhookLogger && eventLogId) {
    try {
      await webhookLogger.updateEventStatus(eventLogId, processingStatus, processingError);
    } catch (logError) {
      console.error('Error updating event log status:', logError);
    }
  }
  return new Response(JSON.stringify({
    ok: true,
    event_id: receivedEvent.id,
    event_type: receivedEvent.type,
    processing_status: processingStatus
  }), {
    status: 200
  });
});
